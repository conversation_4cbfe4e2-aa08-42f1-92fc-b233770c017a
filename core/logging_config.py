import logging
import logging.handlers
import os
from pathlib import Path
from config.settings import settings


class LoggingConfig:
    def __init__(self):
        self.log_dir = Path(settings.LOG_STORAGE_PATH)
        self.log_dir.mkdir(parents=True, exist_ok=True)

        self.error_log_file = self.log_dir / "error.log"
        self.info_log_file = self.log_dir / "info.log"
        self.debug_log_file = self.log_dir / "debug.log"
        self.access_log_file = self.log_dir / "access.log"

        self.detailed_formatter = logging.Formatter(
            fmt='%(asctime)s | %(levelname)-8s | %(name)s:%(lineno)d | %(funcName)s() | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        self.simple_formatter = logging.Formatter(
            fmt='%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def setup_logging(self, log_level: str = "INFO"):
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))

        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        error_handler = self._create_rotating_file_handler(
            self.error_log_file, logging.ERROR, self.detailed_formatter
        )
        root_logger.addHandler(error_handler)

        info_handler = self._create_rotating_file_handler(
            self.info_log_file, logging.INFO, self.detailed_formatter
        )
        info_handler.addFilter(self._create_level_filter(logging.INFO, logging.WARNING))
        root_logger.addHandler(info_handler)

        if log_level.upper() == "DEBUG":
            debug_handler = self._create_rotating_file_handler(
                self.debug_log_file, logging.DEBUG, self.detailed_formatter
            )
            debug_handler.addFilter(self._create_level_filter(logging.DEBUG, logging.DEBUG))
            root_logger.addHandler(debug_handler)

        access_handler = self._create_rotating_file_handler(
            self.access_log_file, logging.INFO, self.simple_formatter
        )

        access_logger = logging.getLogger("access")
        access_logger.setLevel(logging.INFO)
        access_logger.addHandler(access_handler)
        access_logger.propagate = False

        if os.getenv("ENVIRONMENT", "development") == "development":
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            console_handler.setFormatter(self.simple_formatter)
            root_logger.addHandler(console_handler)

        logger = logging.getLogger(__name__)
        logger.info(f"日志系统已启动 - 级别: {log_level}, 日志目录: {self.log_dir}")
        logger.info(f"错误日志: {self.error_log_file}")
        logger.info(f"信息日志: {self.info_log_file}")
        logger.info(f"访问日志: {self.access_log_file}")
        if log_level.upper() == "DEBUG":
            logger.info(f"调试日志: {self.debug_log_file}")
    
    def _create_rotating_file_handler(self, log_file: Path, level: int, formatter: logging.Formatter):
        handler = logging.handlers.RotatingFileHandler(
            filename=log_file, maxBytes=10 * 1024 * 1024, backupCount=5, encoding='utf-8'
        )
        handler.setLevel(level)
        handler.setFormatter(formatter)
        return handler

    def _create_level_filter(self, min_level: int, max_level: int):
        def level_filter(record):
            return min_level <= record.levelno <= max_level
        return level_filter

    def get_access_logger(self):
        return logging.getLogger("access")


logging_config = LoggingConfig()


def setup_application_logging(log_level: str = "INFO"):
    logging_config.setup_logging(log_level)


def get_logger(name: str = None) -> logging.Logger:
    return logging.getLogger(name)


def get_access_logger() -> logging.Logger:
    return logging_config.get_access_logger()


def log_api_access(method: str, path: str, status_code: int, response_time: float, client_ip: str = None):
    access_logger = get_access_logger()
    client_info = f" | IP: {client_ip}" if client_ip else ""
    access_logger.info(
        f"{method} {path} | Status: {status_code} | Time: {response_time:.2f}ms{client_info}"
    )


def log_error_with_context(logger: logging.Logger, message: str, error: Exception = None, **context):
    context_str = " | ".join([f"{k}: {v}" for k, v in context.items()])
    full_message = f"{message}"
    if context_str:
        full_message += f" | Context: {context_str}"

    if error:
        logger.error(full_message, exc_info=True)
    else:
        logger.error(full_message)
