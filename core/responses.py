from typing import Any, Optional
from pydantic import BaseModel
from core.errors import ErrorCodes


class BaseResponse(BaseModel):
    code: int
    message: str
    data: Optional[Any] = None
    success: bool


class SuccessResponse(BaseResponse):
    def __init__(self, data: Any = None, message: str = "操作成功"):
        super().__init__(
            code=ErrorCodes.SUCCESS,
            message=message,
            data=data,
            success=True
        )


class ErrorResponse(BaseResponse):
    def __init__(self, code: int, message: str, data: Any = None):
        super().__init__(
            code=code,
            message=message,
            data=data,
            success=False
        )


def success_response(data: Any = None, message: str = "操作成功") -> dict:
    return SuccessResponse(data=data, message=message).model_dump()


def error_response(code: int, message: str, data: Any = None) -> dict:
    return ErrorResponse(code=code, message=message, data=data).model_dump()
