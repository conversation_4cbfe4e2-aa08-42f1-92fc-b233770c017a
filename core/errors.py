class ErrorCodes:
    # HTTP状态码
    SUCCESS = 200
    BAD_REQUEST = 400
    UNAUTHORIZED_HTTP = 401
    FORBIDDEN_HTTP = 403
    NOT_FOUND_HTTP = 404
    METHOD_NOT_ALLOWED_HTTP = 405
    REQUEST_TIMEOUT = 408
    INTERNAL_SERVER_ERROR = 500

    # 业务错误码
    UNKNOWN_ERROR = 1000
    INVALID_PARAMS = 1001
    UNAUTHORIZED = 1002
    FORBIDDEN = 1003
    NOT_FOUND = 1004
    METHOD_NOT_ALLOWED = 1005
    RATE_LIMITED = 1006

    DOUYIN_API_ERROR = 2000
    DOUYIN_AUTH_FAILED = 2001
    DOUYIN_UPLOAD_FAILED = 2002
    DOUYIN_INVALID_VIDEO = 2003
    DOUYIN_QUOTA_EXCEEDED = 2004
    DOUYIN_LOGIN_SESSION_NOT_FOUND = 2005
    DOUYIN_LOGIN_SESSION_EXPIRED = 2006
    DOUYIN_LOGIN_TIMEOUT = 2007
    DOUYIN_BROWSER_ERROR = 2008
    DOUYIN_COOKIE_EXTRACT_FAILED = 2009
    DOUYIN_SEARCH_FAILED = 2010
    DOUYIN_SEARCH_INVALID_KEYWORD = 2011
    DOUYIN_SEARCH_NO_RESULTS = 2012
    DOUYIN_COMMENT_FAILED = 2013
    DOUYIN_OA_AUTH_FAILED = 2014
    DOUYIN_OA_TOKEN_EXPIRED = 2015
    DOUYIN_OA_INVALID_STATE = 2016

    XIAOHONGSHU_API_ERROR = 3000
    XIAOHONGSHU_AUTH_FAILED = 3001
    XIAOHONGSHU_UPLOAD_FAILED = 3002
    XIAOHONGSHU_INVALID_CONTENT = 3003
    XIAOHONGSHU_QUOTA_EXCEEDED = 3004

    DATABASE_ERROR = 4000
    DATABASE_CONNECTION_FAILED = 4001
    DATABASE_QUERY_FAILED = 4002
    DATABASE_DUPLICATE_ENTRY = 4003

    AI_SERVICE_ERROR = 5000
    AI_API_ERROR = 5001
    AI_AUTH_FAILED = 5002
    AI_QUOTA_EXCEEDED = 5003
    AI_INVALID_INPUT = 5004
    AI_RESPONSE_PARSE_ERROR = 5005

    DEVICE_VERIFICATION_ERROR = 6000
    DEVICE_FINGERPRINT_FAILED = 6001
    DEVICE_NOT_AUTHORIZED = 6002

    # 网络安全错误码
    NETWORK_SECURITY_ERROR = 7000
    NETWORK_RESTRICTED = 7001
    NETWORK_DETECTION_FAILED = 7002


class BaseServiceException(Exception):
    def __init__(self, message: str, error_code: int = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or ErrorCodes.UNKNOWN_ERROR


class DouyinServiceException(BaseServiceException):
    def __init__(self, message: str, error_code: int = None):
        super().__init__(message, error_code or ErrorCodes.DOUYIN_API_ERROR)


class DouyinSessionException(BaseServiceException):
    def __init__(self, message: str, error_code: int = None):
        super().__init__(message, error_code or ErrorCodes.DOUYIN_LOGIN_SESSION_NOT_FOUND)
