@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --bg-primary: 255 255 255;
    --bg-secondary: 248 250 252;
    --text-primary: 15 23 42;
    --text-secondary: 100 116 139;
    --border-color: 226 232 240;
  }

  .dark {
    /* Dark theme colors */
    --bg-primary: 15 23 42;
    --bg-secondary: 30 41 59;
    --text-primary: 255 255 255;
    --text-secondary: 203 213 225;
    --border-color: 71 85 105;
  }

  * {
    box-sizing: border-box;
  }

  body {
    @apply antialiased transition-colors duration-300;
    margin: 0;
    min-height: 100vh;
    font-family: system-ui, -apple-system, sans-serif;
    background-color: rgb(var(--bg-primary));
    color: rgb(var(--text-primary));
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .btn-secondary {
    @apply bg-dark-700 hover:bg-dark-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }

  .card {
    @apply bg-dark-800 rounded-xl p-6 shadow-lg border border-dark-700;
  }

  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  /* 高性能卡片动画效果 */
  .card-hover-effect {
    transform-origin: center;
    will-change: transform, box-shadow;
  }

  .card-hover-effect:hover {
    transform: translateY(-4px) scale(1.02);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 渐变边框效果 */
  .border-gradient-dark {
    border: 1px solid transparent;
    background: linear-gradient(135deg, rgba(148, 163, 184, 0.1), rgba(71, 85, 105, 0.2)) padding-box,
                linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.2), rgba(59, 130, 246, 0.1)) border-box;
  }

  .border-gradient-light {
    border: 1px solid transparent;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.8)) padding-box,
                linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.1), rgba(59, 130, 246, 0.15)) border-box;
  }

  /* 微妙的背景纹理 */
  .card-texture::before {
    content: '';
    position: absolute;
    inset: 0;
    background-image:
      radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
    background-size: 20px 20px;
    opacity: 0.3;
    pointer-events: none;
  }

  /* React Flow 样式覆盖 */
  .react-flow__edge-path {
    transition: all 0.3s ease;
  }

  .react-flow__edge:hover .react-flow__edge-path {
    stroke-width: 4 !important;
    filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.6));
  }

  /* 深色主题下的 React Flow 样式 */
  .dark .react-flow__controls {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
  }

  .dark .react-flow__controls button {
    background: rgba(51, 65, 85, 0.8);
    border: 1px solid rgba(71, 85, 105, 0.5);
    color: #e2e8f0;
  }

  .dark .react-flow__controls button:hover {
    background: rgba(71, 85, 105, 0.8);
  }

  /* 性能优化：减少重绘 */
  .transition-gpu {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* 高性能阴影动画 */
  @supports (filter: drop-shadow(0 0 0 transparent)) {
    .card-hover-effect:hover {
      filter: drop-shadow(0 20px 25px rgba(0, 0, 0, 0.15)) drop-shadow(0 10px 10px rgba(0, 0, 0, 0.04));
    }
  }

  /* 减少动画期间的重绘 */
  .card-hover-effect {
    contain: layout style paint;
  }

  /* 优化渐变性能 */
  .bg-gradient-to-br {
    background-attachment: fixed;
  }
}
