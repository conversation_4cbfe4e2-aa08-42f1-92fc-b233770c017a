export interface ApiResponse<T = unknown> {
  code: number;
  message: string;
  data: T;
  success: boolean;
  timestamp?: string;
}

export const LoginStatus = {
  PENDING: "pending",
  READY: "ready",
  SCANNING: "scanning",
  SUCCESS: "success",
  FAILED: "failed",
  EXPIRED: "expired",
  CANCELLED: "cancelled"
} as const;

export type LoginStatus = typeof LoginStatus[keyof typeof LoginStatus];

export type LoginPlatform = 'douyin' | 'xiaohongshu';

export interface TopicConfig {
  id: string;
  keyword: string;
}

export interface SearchConfig {
  totalCount: number;
}

export interface LoginSession {
  session_id: string;
  status: LoginStatus;
  created_at: string;
  updated_at: string;
  expires_at: string;
  message: string;
  error_message?: string;
}

export interface LoginStartRequest {
  timeout_minutes?: number;
  headless?: boolean;
}

export interface CookieData {
  name: string;
  value: string;
  domain: string;
  path: string;
  expires?: string;
  secure: boolean;
  http_only: boolean;
  same_site?: string;
}

export interface CookieResponse {
  session_id: string;
  cookies: CookieData[];
  total_count: number;
  extracted_at: string;
}

export interface CookieValidationRequest {
  cookies: CookieData[];
  timeout_seconds?: number;
}

export interface UserInfo {
  uid?: string;
  nickname?: string;
  avatar_thumb?: {
    url_list?: string[];
  } | string;
  [key: string]: unknown;
}

export interface UserData {
  user_id?: string;
  nickname?: string;
  avatar?: string;
  user_info?: UserInfo;
  [key: string]: unknown;
}

export interface CookieValidationResponse {
  is_valid: boolean;
  user_data?: UserData;
  validation_time: string;
  error_message?: string;
  status_code?: number;
}

export interface SearchRequest {
  keyword: string;
  count?: number;
  offset?: number;
  pages?: number;
  per_page?: number;
  cookies?: CookieData[]; // Made optional for V3 API compatibility
  timeout_seconds?: number;
}

export interface VideoStatistics {
  digg_count?: number;
  comment_count?: number;
  share_count?: number;
  play_count?: number;
  [key: string]: number | undefined;
}

export interface VideoInfo {
  aweme_id: string;
  desc: string;
  author_nickname: string;
  author_unique_id: string;
  create_time?: number;
  statistics?: VideoStatistics;
  douyin_link?: string;
}

export interface SearchResult {
  videos: VideoInfo[];
  total_count: number;
  has_more: boolean;
  cursor?: string | number;
}

export interface SearchResponse {
  keyword: string;
  result: SearchResult;
  search_time: string;
  database_stats?: {
    inserted_count: number;
    skipped_count: number;
    error_count: number;
    message?: string;
  };
}

export interface VideoFilterInfo {
  aweme_id: string;
  create_time?: number;
  comment_count?: number;
}

export interface CommentRequest {
  aweme_id?: string;
  video_list?: VideoFilterInfo[];
  cookies: CookieData[];
  max_months_old?: number;
  min_comment_count?: number;
  cursor?: string;
  timeout_seconds?: number;
  fetch_all_comments?: boolean;
  max_comments_limit?: number;
}

export interface CommentInfo {
  cid: string;
  aweme_id: string;
  text: string;
  user_nickname: string;
  user_uid: string;
  user_short_id: string;
  user_sec_uid: string;
  user_avatar: string;
  create_time: number;
  digg_count: number;
  reply_count: number;
  is_author: boolean;
  ip_label: string;
}

export interface SkippedVideoInfo {
  aweme_id: string;
  reason: string;
  create_time?: number;
  comment_count?: number;
}

export interface CommentResult {
  comments: CommentInfo[];
  has_more: boolean;
  cursor: string;
  total: number;
  message: string;
  fetched_count: number;
  is_limited: boolean;
  pages_fetched: number;
  processed_videos: string[];
  skipped_videos: SkippedVideoInfo[];
  filter_stats: Record<string, number>;
}

// 用户信息相关类型定义
export interface UserInfoRequest {
  sec_uid: string | string[];
  cookies: CookieData[] | string;
  timeout_seconds?: number;
}

export interface DouyinUserInfo {
  short_id: string;
  nickname: string;
  signature: string;
  uid?: string;
  sec_uid?: string;
  avatar_url?: string;
}

export interface UserInfoResult {
  users: DouyinUserInfo[];
  total_requested: number;
  total_found: number;
  message: string;
}



export interface CommentCustomerAnalysisRequest {
  comments: CommentInfo[];
  group_by_user?: boolean;
  min_confidence?: number;
  timeout_seconds?: number;
  max_concurrent_requests?: number;
}

export interface CustomerIntent {
  user_nickname: string;
  user_uid: string;
  user_short_id: string;
  user_sec_uid: string;
  intent_type: string;
  confidence: number;
  reasoning: string;
  comments_analyzed: string[];
  comment_count: number;
  video_ids?: string[];
  video_links?: string[];
  comment_times?: string[];
  ip_labels?: string[];
  latest_comment_time?: string;
}

export interface DatabaseSaveResult {
  success_count: number;
  skipped_count: number;
  failed_count: number;
  failed_users: string[];
  total_count: number;
}

export interface TokenCostInfo {
  total_cost: number;
  total_tokens: number;
  call_count: number;
  model_breakdown: Record<string, {
    total_cost: number;
    input_tokens: number;
    output_tokens: number;
    call_count: number;
  }>;
}

export interface CommentCustomerAnalysisResult {
  potential_customers: CustomerIntent[];
  total_users_analyzed: number;
  total_comments_analyzed: number;
  processing_time: number;
  model_used: string;
  database_save_result?: DatabaseSaveResult;
  token_cost_info?: TokenCostInfo;
}

export interface RealEstateAnalysisRequest {
  admin_password: string;
  videos: VideoInfo[];
  timeout_seconds?: number;
}

export interface RealEstateAnalysisResult {
  aweme_id: string;
  video_description: string;
  is_real_estate_related: boolean;
  confidence_score: number;
  analysis_reasoning: string;
  keywords_found: string[];
  category?: string;
  intent_type?: string;
  // 评论过滤所需字段
  create_time?: number;
  comment_count?: number;
}

export interface RealEstateAnalysisResponse {
  total_analyzed: number;
  real_estate_count: number;
  non_real_estate_count: number;
  results: RealEstateAnalysisResult[];
  processing_time: number;
  success_rate: number;
}

export interface DeviceInfo {
  cpu_cores?: number;
  cpu_architecture?: string;
  memory_size?: number;
  gpu_vendor?: string;
  gpu_renderer?: string;
  screen_width?: number;
  screen_height?: number;
  screen_color_depth?: number;
  pixel_ratio?: number;
  user_agent?: string;
  platform?: string;
  language?: string;
  timezone?: string;
  canvas_fingerprint?: string;
  webgl_fingerprint?: string;
  touch_support?: boolean;
  cookie_enabled?: boolean;
  collected_at: string;
}

export interface DeviceFingerprint {
  fingerprint: string;
  device_info: DeviceInfo;
  generated_at: string;
}

export interface DeviceVerificationRequest {
  encrypted_device_info: string;
}

export interface DeviceVerificationResponse {
  is_developer: boolean;
  device_fingerprint: string;
  verification_time: string;
  message: string;
}

export interface AccountInfo {
  id: string;
  platform: LoginPlatform;
  nickname?: string;
  avatar?: string;
  is_valid: boolean;
  created_at: string;
  [key: string]: unknown;
}

export interface AccountManagerProps {
  isDeveloper: boolean;
}

export interface NetworkSecurity {
  is_safe: boolean;
  current_network: string | null;
  message: string;
  restricted: boolean;
}

export interface HealthCheckResponse {
  status: string;
  service: string;
  network_security: NetworkSecurity;
}

// 抖音官方OA登录相关类型定义
export interface OAScope {
  USER_INFO: 'user_info';
  VIDEO_LIST: 'video.list';
  VIDEO_DATA: 'video.data';
  FANS_DATA: 'fans.data';
}

export interface OAAuthRequest {
  scope?: string[];
}

export interface OAAuthUrlResponse {
  auth_url: string;
  state: string;
  expires_in: number;
  created_at: string;
}

export interface OACallbackRequest {
  code: string;
  state?: string;
}

export interface OATokenInfo {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  scope: string[];
  open_id: string;
  created_at: string;
}

export interface OAUserInfo {
  open_id: string;
  union_id?: string;
  nickname: string;
  avatar?: string;
  gender?: number;
  city?: string;
  province?: string;
  country?: string;
}

export interface OATokenValidateRequest {
  access_token: string;
  open_id: string;
}

export interface OATokenValidateResponse {
  is_valid: boolean;
  expires_in?: number;
  scope?: string[];
  error_message?: string;
  validated_at: string;
}

// 扩展现有的DouyinAccount接口以支持OA登录
export interface DouyinOAAccount {
  id: string;
  nickname: string;
  open_id: string;
  union_id?: string;
  avatar?: string;
  access_token: string;
  refresh_token: string;
  expires_in: number;
  refresh_expires_in: number;
  scope: string[];
  addedAt: string;
  lastValidation?: string;
  isValid?: boolean;
  userInfo?: OAUserInfo;
  loginType: 'oa'; // 区分登录类型
}
