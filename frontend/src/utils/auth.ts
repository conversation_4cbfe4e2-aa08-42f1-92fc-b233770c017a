import { douyinApi } from '../services/api';
import { MultiAccountManager } from './multiAccount';
import type { CookieValidationResponse, OAUserInfo } from '../types';
const STORAGE_KEYS = {
  DOUYIN_COOKIES: 'douyin-cookies',
  DOUYIN_SESSION_ID: 'douyin-session-id',
  DOUYIN_LOGIN_STATUS: 'douyin-login-status',
  DOUYIN_LAST_VALIDATION: 'douyin-last-validation'
} as const;
export interface DouyinLoginStatus {
  isLoggedIn: boolean;
  lastValidation: string;
  userInfo?: CookieValidationResponse;
}


export async function checkDouyinLoginStatus(forceValidate: boolean = false): Promise<DouyinLoginStatus> {
  try {
    MultiAccountManager.migrateFromSingleAccount();

    const currentAccount = MultiAccountManager.getCurrentAccount();
    if (currentAccount) {
      if (!forceValidate && currentAccount.lastValidation) {
        const lastValidationTime = new Date(currentAccount.lastValidation);
        const now = new Date();
        const timeDiff = now.getTime() - lastValidationTime.getTime();

        if (timeDiff < 5 * 60 * 1000 && currentAccount.isValid) {
          return {
            isLoggedIn: true,
            lastValidation: currentAccount.lastValidation,
            userInfo: currentAccount.userInfo
          };
        }
      }

      const isValid = await MultiAccountManager.validateAccount(currentAccount.id);
      const updatedAccount = MultiAccountManager.getCurrentAccount();

      return {
        isLoggedIn: isValid,
        lastValidation: new Date().toISOString(),
        userInfo: updatedAccount?.userInfo
      };
    }

    const cookieData = localStorage.getItem(STORAGE_KEYS.DOUYIN_COOKIES);
    if (!cookieData) {
      return {
        isLoggedIn: false,
        lastValidation: new Date().toISOString()
      };
    }

    if (!forceValidate) {
      const cachedStatus = localStorage.getItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS);
      const lastValidation = localStorage.getItem(STORAGE_KEYS.DOUYIN_LAST_VALIDATION);

      if (cachedStatus && lastValidation) {
        const lastValidationTime = new Date(lastValidation);
        const now = new Date();
        const timeDiff = now.getTime() - lastValidationTime.getTime();

        if (timeDiff < 5 * 60 * 1000) {
          const status: DouyinLoginStatus = JSON.parse(cachedStatus);
          return status;
        }
      }
    }

    const parsedCookieData = JSON.parse(cookieData);

    const validationResult = await douyinApi.validateCookies({
      cookies: parsedCookieData.cookies,
      timeout_seconds: 10
    });

    const loginStatus: DouyinLoginStatus = {
      isLoggedIn: validationResult.is_valid,
      lastValidation: new Date().toISOString(),
      userInfo: validationResult.is_valid ? validationResult : undefined
    };

    localStorage.setItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS, JSON.stringify(loginStatus));
    localStorage.setItem(STORAGE_KEYS.DOUYIN_LAST_VALIDATION, loginStatus.lastValidation);

    return loginStatus;

  } catch {
    localStorage.removeItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS);

    return {
      isLoggedIn: false,
      lastValidation: new Date().toISOString()
    };
  }
}

export function isDouyinLoggedIn(): boolean {
  // 优先检查多账号系统
  const currentAccount = MultiAccountManager.getCurrentAccount();
  if (currentAccount) {
    return currentAccount.isValid === true;
  }

  // 回退到旧版本逻辑
  const cookieData = localStorage.getItem(STORAGE_KEYS.DOUYIN_COOKIES);
  const loginStatus = localStorage.getItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS);

  if (!cookieData || !loginStatus) {
    return false;
  }

  try {
    const status: DouyinLoginStatus = JSON.parse(loginStatus);
    return status.isLoggedIn;
  } catch {
    return false;
  }
}

export function clearDouyinLoginData(): void {
  MultiAccountManager.clearAllAccounts();

  localStorage.removeItem(STORAGE_KEYS.DOUYIN_COOKIES);
  localStorage.removeItem(STORAGE_KEYS.DOUYIN_SESSION_ID);
  localStorage.removeItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS);
  localStorage.removeItem(STORAGE_KEYS.DOUYIN_LAST_VALIDATION);
}

export function getCachedUserInfo(): CookieValidationResponse | null {
  try {
    const currentAccount = MultiAccountManager.getCurrentAccount();
    if (currentAccount && currentAccount.userInfo) {
      return currentAccount.userInfo;
    }

    const loginStatus = localStorage.getItem(STORAGE_KEYS.DOUYIN_LOGIN_STATUS);
    if (!loginStatus) return null;

    const status: DouyinLoginStatus = JSON.parse(loginStatus);
    return status.userInfo || null;
  } catch {
    return null;
  }
}

// OA登录状态接口
export interface DouyinOALoginStatus {
  isLoggedIn: boolean;
  lastValidation: string;
  userInfo?: OAUserInfo;
  loginType: 'oa';
}

// 检查OA登录状态
export async function checkDouyinOALoginStatus(forceValidate: boolean = false): Promise<DouyinOALoginStatus> {
  try {
    const currentAccount = MultiAccountManager.getCurrentOAAccount();
    if (currentAccount) {
      if (!forceValidate && currentAccount.lastValidation) {
        const lastValidationTime = new Date(currentAccount.lastValidation);
        const now = new Date();
        const timeDiff = now.getTime() - lastValidationTime.getTime();

        // 如果5分钟内验证过且有效，直接返回
        if (timeDiff < 5 * 60 * 1000 && currentAccount.isValid) {
          return {
            isLoggedIn: true,
            lastValidation: currentAccount.lastValidation,
            userInfo: currentAccount.userInfo,
            loginType: 'oa'
          };
        }
      }

      // 验证token有效性
      const isValid = await MultiAccountManager.validateOAAccount(currentAccount.id);

      return {
        isLoggedIn: isValid,
        lastValidation: new Date().toISOString(),
        userInfo: isValid ? currentAccount.userInfo : undefined,
        loginType: 'oa'
      };
    }

    return {
      isLoggedIn: false,
      lastValidation: new Date().toISOString(),
      loginType: 'oa'
    };
  } catch (error) {
    console.error('检查OA登录状态失败:', error);
    return {
      isLoggedIn: false,
      lastValidation: new Date().toISOString(),
      loginType: 'oa'
    };
  }
}

// 获取当前OA用户信息
export function getCurrentOAUserInfo(): OAUserInfo | null {
  try {
    const currentAccount = MultiAccountManager.getCurrentOAAccount();
    return currentAccount?.userInfo || null;
  } catch {
    return null;
  }
}

// 统一的登录状态检查（支持两种登录方式）
export async function checkAnyDouyinLoginStatus(forceValidate: boolean = false): Promise<{
  hasLogin: boolean;
  loginType?: 'nodriver' | 'oa';
  userInfo?: CookieValidationResponse | OAUserInfo;
}> {
  try {
    // 先检查nodriver登录
    const nodriverStatus = await checkDouyinLoginStatus(forceValidate);
    if (nodriverStatus.isLoggedIn) {
      return {
        hasLogin: true,
        loginType: 'nodriver',
        userInfo: nodriverStatus.userInfo
      };
    }

    // 再检查OA登录
    const oaStatus = await checkDouyinOALoginStatus(forceValidate);
    if (oaStatus.isLoggedIn) {
      return {
        hasLogin: true,
        loginType: 'oa',
        userInfo: oaStatus.userInfo
      };
    }

    return { hasLogin: false };
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return { hasLogin: false };
  }
}
