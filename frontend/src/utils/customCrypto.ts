/**
 * 自定义加密工具类
 */
export class CustomCrypto {

  /**
   * 简单加密
   */
  static encrypt(data: string, key: string): string {
    if (!data || !key) return '';

    try {
      let result = '';
      const encoder = new TextEncoder();
      const dataBytes = encoder.encode(data);

      for (let i = 0; i < dataBytes.length; i++) {
        const dataByte = dataBytes[i];
        const keyByte = key.charCodeAt(i % key.length);
        const encrypted = dataByte ^ keyByte;
        result += encrypted.toString(16).padStart(2, '0');
      }

      return result;
    } catch {
      return '';
    }
  }

  /**
   * 简单解密
   */
  static decrypt(encryptedData: string, key: string): string {
    if (!encryptedData || !key || encryptedData.length % 2 !== 0) return '';

    try {
      const bytes: number[] = [];

      for (let i = 0; i < encryptedData.length; i += 2) {
        const encryptedByte = parseInt(encryptedData.substring(i, i + 2), 16);
        const keyByte = key.charCodeAt((i / 2) % key.length);
        const decrypted = encryptedByte ^ keyByte;
        bytes.push(decrypted);
      }

      const decoder = new TextDecoder();
      const uint8Array = new Uint8Array(bytes);
      return decoder.decode(uint8Array);
    } catch {
      return '';
    }
  }
}
