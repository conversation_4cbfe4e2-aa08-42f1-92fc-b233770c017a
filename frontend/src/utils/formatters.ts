export const formatTime = (
  timestamp?: number,
  options: {
    includeSeconds?: boolean;
    fallback?: string;
  } = {}
): string => {
  const { includeSeconds = true, fallback = '未知时间' } = options;
  
  if (!timestamp) return fallback;
  
  const date = new Date(timestamp * 1000);
  
  if (includeSeconds) {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

export const formatNumber = (num?: number, fallback: string = '0'): string => {
  if (!num) return fallback;
  
  if (num >= 10000) {
    return `${(num / 10000).toFixed(1)}万`;
  }
  
  return num.toString();
};

export const formatTimeString = (timeString: string): string => {
  return new Date(timeString).toLocaleString('zh-CN');
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  return `${(value * 100).toFixed(decimals)}%`;
};
