import { douyin<PERSON>pi, douy<PERSON><PERSON><PERSON><PERSON> } from '../services/api';
import type { CookieValidationResponse, CookieData, DouyinOAAccount, OATokenInfo, OAUserInfo } from '../types';

interface UserAvatarInfo {
  avatar_thumb?: {
    url_list?: string[];
  } | string;
}


export const MULTI_ACCOUNT_STORAGE_KEYS = {
  DOUYIN_ACCOUNTS: 'douyin-accounts',
  DOUYIN_CURRENT_ACCOUNT: 'douyin-current-account',
  DOUYIN_ACCOUNT_COUNTER: 'douyin-account-counter',
  DOUYIN_OA_ACCOUNTS: 'douyin-oa-accounts',
  DOUYIN_OA_CURRENT_ACCOUNT: 'douyin-oa-current-account',
  DOUYIN_OA_ACCOUNT_COUNTER: 'douyin-oa-account-counter'
} as const;
export interface DouyinAccount {
  id: string;
  nickname: string;
  uid: string;
  avatar?: string;
  cookies: CookieData[];
  searchCookies?: CookieData[]; // Deprecated: V3 API不需要搜索Cookie
  sessionId: string;
  addedAt: string;
  lastValidation?: string;
  isValid?: boolean;
  userInfo?: CookieValidationResponse;
}
export class MultiAccountManager {
  static getAccounts(): DouyinAccount[] {
    try {
      const accountsData = localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS);
      return accountsData ? JSON.parse(accountsData) : [];
    } catch {
      return [];
    }
  }
  static getCurrentAccountId(): string | null {
    return localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_CURRENT_ACCOUNT);
  }
  static getCurrentAccount(): DouyinAccount | null {
    const currentId = this.getCurrentAccountId();
    if (!currentId) return null;

    const accounts = this.getAccounts();
    return accounts.find(account => account.id === currentId) || null;
  }
  static generateAccountId(): string {
    const counter = parseInt(localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNT_COUNTER) || '0') + 1;
    localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNT_COUNTER, counter.toString());
    return `account_${counter}_${Date.now()}`;
  }
  static async addAccount(
    cookies: CookieData[],
    sessionId: string,
    userInfo: CookieValidationResponse
  ): Promise<DouyinAccount> {
    const accounts = this.getAccounts();

    const existingAccount = accounts.find(account =>
      account.uid === userInfo.user_data?.user_info?.uid
    );

    if (existingAccount) {
      throw new Error('该账号已存在，请勿重复添加');
    }

    const newAccount: DouyinAccount = {
      id: this.generateAccountId(),
      nickname: userInfo.user_data?.user_info?.nickname || '抖音用户',
      uid: userInfo.user_data?.user_info?.uid || '',
      avatar: this.extractAvatarUrl(userInfo.user_data?.user_info),
      cookies,
      sessionId,
      addedAt: new Date().toISOString(),
      lastValidation: new Date().toISOString(),
      isValid: true,
      userInfo
    };

    accounts.push(newAccount);
    localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify(accounts));

    if (accounts.length === 1) {
      this.setCurrentAccount(newAccount.id);
    }

    return newAccount;
  }
  private static extractAvatarUrl(userInfo: UserAvatarInfo | undefined): string | undefined {
    if (!userInfo?.avatar_thumb) return undefined;

    if (typeof userInfo.avatar_thumb === 'string') {
      return userInfo.avatar_thumb;
    } else if (typeof userInfo.avatar_thumb === 'object' && userInfo.avatar_thumb.url_list?.[0]) {
      return userInfo.avatar_thumb.url_list[0];
    }
    return undefined;
  }
  static removeAccount(accountId: string): boolean {
    try {
      const accounts = this.getAccounts();
      const filteredAccounts = accounts.filter(account => account.id !== accountId);

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify(filteredAccounts));

      const currentId = this.getCurrentAccountId();
      if (currentId === accountId) {
        if (filteredAccounts.length > 0) {
          this.setCurrentAccount(filteredAccounts[0].id);
        } else {
          localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_CURRENT_ACCOUNT);
        }
      }

      if (filteredAccounts.length === 0) {
        localStorage.removeItem('douyin-cookies');
        localStorage.removeItem('douyin-session-id');
        localStorage.removeItem('douyin-login-status');
        localStorage.removeItem('douyin-last-validation');
      }

      return true;
    } catch {
      return false;
    }
  }
  static setCurrentAccount(accountId: string): boolean {
    try {
      const accounts = this.getAccounts();
      const account = accounts.find(acc => acc.id === accountId);

      if (!account) {
        throw new Error('账号不存在');
      }

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_CURRENT_ACCOUNT, accountId);

      const cookieData = {
        cookies: account.cookies,
        session_id: account.sessionId,
        extracted_at: account.addedAt
      };
      localStorage.setItem('douyin-cookies', JSON.stringify(cookieData));
      localStorage.setItem('douyin-session-id', account.sessionId);

      if (account.userInfo) {
        const loginStatus = {
          isLoggedIn: true,
          lastValidation: account.lastValidation || new Date().toISOString(),
          userInfo: account.userInfo
        };
        localStorage.setItem('douyin-login-status', JSON.stringify(loginStatus));
        localStorage.setItem('douyin-last-validation', loginStatus.lastValidation);
      }

      return true;
    } catch {
      return false;
    }
  }
  static async validateAccount(accountId: string): Promise<boolean> {
    try {
      const accounts = this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);

      if (accountIndex === -1) {
        return false;
      }

      const account = accounts[accountIndex];

      const validationResult = await douyinApi.validateCookies({
        cookies: account.cookies,
        timeout_seconds: 10
      });

      accounts[accountIndex] = {
        ...account,
        isValid: validationResult.is_valid,
        lastValidation: new Date().toISOString(),
        userInfo: validationResult.is_valid ? validationResult : account.userInfo
      };

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify(accounts));

      return validationResult.is_valid;
    } catch {
      return false;
    }
  }
  static canAddAccount(isDeveloper: boolean): { canAdd: boolean; reason?: string } {
    const accounts = this.getAccounts();

    if (isDeveloper) {
      return { canAdd: true };
    } else {
      if (accounts.length >= 1) {
        return {
          canAdd: false,
          reason: '普通用户只能添加一个账号，如需添加更多账号请联系管理员获取开发者权限'
        };
      }
      return { canAdd: true };
    }
  }
  static migrateFromSingleAccount(): boolean {
    try {
      const existingAccounts = this.getAccounts();
      if (existingAccounts.length > 0) {
        return false;
      }

      const oldCookieData = localStorage.getItem('douyin-cookies');
      const oldLoginStatus = localStorage.getItem('douyin-login-status');
      const oldSessionId = localStorage.getItem('douyin-session-id');

      if (!oldCookieData || !oldLoginStatus) {
        return false;
      }

      const cookieData = JSON.parse(oldCookieData);
      const loginStatus = JSON.parse(oldLoginStatus);

      if (loginStatus.isLoggedIn && loginStatus.userInfo) {
        const migratedAccount: DouyinAccount = {
          id: this.generateAccountId(),
          nickname: loginStatus.userInfo.user_data?.user_info?.nickname || '抖音用户',
          uid: loginStatus.userInfo.user_data?.user_info?.uid || '',
          avatar: this.extractAvatarUrl(loginStatus.userInfo.user_data?.user_info),
          cookies: cookieData.cookies,
          sessionId: oldSessionId || '',
          addedAt: new Date().toISOString(),
          lastValidation: loginStatus.lastValidation,
          isValid: true,
          userInfo: loginStatus.userInfo
        };

        localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify([migratedAccount]));
        this.setCurrentAccount(migratedAccount.id);

        return true;
      }

      return false;
    } catch {
      return false;
    }
  }
  static async updateAccountSearchCookies(accountId: string, cookieString: string): Promise<boolean> {
    try {
      const accounts = this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);

      if (accountIndex === -1) {
        return false;
      }


      const searchCookies: CookieData[] = [];
      const cookiePairs = cookieString.split(';');

      for (const pair of cookiePairs) {
        const trimmedPair = pair.trim();
        if (trimmedPair && trimmedPair.includes('=')) {
          const [name, ...valueParts] = trimmedPair.split('=');
          const value = valueParts.join('=');

          searchCookies.push({
            name: name.trim(),
            value: value.trim(),
            domain: '.douyin.com',
            path: '/',
            expires: undefined,
            secure: false,
            http_only: false,
            same_site: undefined
          });
        }
      }

      if (searchCookies.length === 0) {
        return false;
      }


      accounts[accountIndex] = {
        ...accounts[accountIndex],
        searchCookies,
        lastValidation: new Date().toISOString()
      };

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify(accounts));
      return true;
    } catch (error) {
      return false;
    }
  }

  static async updateAccountCookies(accountId: string, cookieString: string): Promise<boolean> {
    try {
      const accounts = this.getAccounts();
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);

      if (accountIndex === -1) {
        return false;
      }


      const cookies: CookieData[] = [];
      const cookiePairs = cookieString.split(';');

      for (const pair of cookiePairs) {
        const trimmedPair = pair.trim();
        if (trimmedPair && trimmedPair.includes('=')) {
          const [name, ...valueParts] = trimmedPair.split('=');
          const value = valueParts.join('=');

          cookies.push({
            name: name.trim(),
            value: value.trim(),
            domain: '.douyin.com',
            path: '/',
            expires: undefined,
            secure: false,
            http_only: false,
            same_site: undefined
          });
        }
      }

      if (cookies.length === 0) {
        return false;
      }


      accounts[accountIndex] = {
        ...accounts[accountIndex],
        cookies,
        lastValidation: new Date().toISOString()
      };


      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS, JSON.stringify(accounts));

      return true;
    } catch (error) {
      return false;
    }
  }

  static clearAllAccounts(): void {
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNTS);
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_CURRENT_ACCOUNT);
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_ACCOUNT_COUNTER);
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS);
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_CURRENT_ACCOUNT);
    localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNT_COUNTER);

    localStorage.removeItem('douyin-cookies');
    localStorage.removeItem('douyin-session-id');
    localStorage.removeItem('douyin-login-status');
    localStorage.removeItem('douyin-last-validation');
  }

  // OA账户管理方法
  static getOAAccounts(): DouyinOAAccount[] {
    try {
      const accountsData = localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS);
      return accountsData ? JSON.parse(accountsData) : [];
    } catch {
      return [];
    }
  }

  static getCurrentOAAccountId(): string | null {
    return localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_CURRENT_ACCOUNT);
  }

  static getCurrentOAAccount(): DouyinOAAccount | null {
    const currentId = this.getCurrentOAAccountId();
    if (!currentId) return null;

    const accounts = this.getOAAccounts();
    return accounts.find(account => account.id === currentId) || null;
  }

  static generateOAAccountId(): string {
    const counter = parseInt(localStorage.getItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNT_COUNTER) || '0') + 1;
    localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNT_COUNTER, counter.toString());
    return `oa_account_${counter}`;
  }

  static async addOAAccount(
    tokenInfo: OATokenInfo,
    userInfo: OAUserInfo
  ): Promise<DouyinOAAccount> {
    const accounts = this.getOAAccounts();

    const existingAccountIndex = accounts.findIndex(account =>
      account.open_id === userInfo.open_id
    );

    if (existingAccountIndex !== -1) {
      // 更新现有账号信息
      const existingAccount = accounts[existingAccountIndex];
      const updatedAccount: DouyinOAAccount = {
        ...existingAccount,
        nickname: userInfo.nickname,
        avatar: userInfo.avatar,
        access_token: tokenInfo.access_token,
        refresh_token: tokenInfo.refresh_token,
        expires_in: tokenInfo.expires_in,
        refresh_expires_in: tokenInfo.refresh_expires_in,
        scope: tokenInfo.scope,
        userInfo,
        lastValidation: new Date().toISOString(),
        isValid: true
      };

      accounts[existingAccountIndex] = updatedAccount;
      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS, JSON.stringify(accounts));

      return updatedAccount;
    }

    const newAccount: DouyinOAAccount = {
      id: this.generateOAAccountId(),
      nickname: userInfo.nickname,
      open_id: userInfo.open_id,
      union_id: userInfo.union_id,
      avatar: userInfo.avatar,
      access_token: tokenInfo.access_token,
      refresh_token: tokenInfo.refresh_token,
      expires_in: tokenInfo.expires_in,
      refresh_expires_in: tokenInfo.refresh_expires_in,
      scope: tokenInfo.scope,
      addedAt: new Date().toISOString(),
      lastValidation: new Date().toISOString(),
      isValid: true,
      userInfo,
      loginType: 'oa'
    };

    accounts.push(newAccount);
    localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS, JSON.stringify(accounts));

    // 如果是第一个账号，设为当前账号
    if (accounts.length === 1) {
      this.setCurrentOAAccount(newAccount.id);
    }

    return newAccount;
  }

  static setCurrentOAAccount(accountId: string): boolean {
    try {
      const accounts = this.getOAAccounts();
      const account = accounts.find(acc => acc.id === accountId);

      if (!account) {
        throw new Error('账号不存在');
      }

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_CURRENT_ACCOUNT, accountId);
      return true;
    } catch {
      return false;
    }
  }

  static removeOAAccount(accountId: string): boolean {
    try {
      const accounts = this.getOAAccounts();
      const filteredAccounts = accounts.filter(account => account.id !== accountId);

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS, JSON.stringify(filteredAccounts));

      // 如果删除的是当前账号，清除当前账号设置
      const currentAccountId = this.getCurrentOAAccountId();
      if (currentAccountId === accountId) {
        localStorage.removeItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_CURRENT_ACCOUNT);

        // 如果还有其他账号，设置第一个为当前账号
        if (filteredAccounts.length > 0) {
          this.setCurrentOAAccount(filteredAccounts[0].id);
        }
      }

      return true;
    } catch {
      return false;
    }
  }

  static async validateOAAccount(accountId: string): Promise<boolean> {
    try {
      const accounts = this.getOAAccounts();
      const account = accounts.find(acc => acc.id === accountId);

      if (!account) {
        return false;
      }

      const validationResult = await douyinOAApi.validateToken({
        access_token: account.access_token,
        open_id: account.open_id
      });

      // 更新账号验证状态
      account.isValid = validationResult.is_valid;
      account.lastValidation = new Date().toISOString();

      // 如果token过期，尝试刷新
      if (!validationResult.is_valid && account.refresh_token) {
        try {
          // 这里需要client_key，暂时使用空字符串，实际应该从配置获取
          const newTokenInfo = await douyinOAApi.refreshToken(account.refresh_token, '');

          // 更新token信息
          account.access_token = newTokenInfo.access_token;
          account.refresh_token = newTokenInfo.refresh_token;
          account.expires_in = newTokenInfo.expires_in;
          account.refresh_expires_in = newTokenInfo.refresh_expires_in;
          account.isValid = true;
        } catch (refreshError) {
          account.isValid = false;
        }
      }

      localStorage.setItem(MULTI_ACCOUNT_STORAGE_KEYS.DOUYIN_OA_ACCOUNTS, JSON.stringify(accounts));
      return account.isValid;
    } catch {
      return false;
    }
  }
}
