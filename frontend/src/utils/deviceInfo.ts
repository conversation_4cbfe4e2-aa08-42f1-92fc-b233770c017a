import { CustomCrypto } from './customCrypto';

export interface DeviceInfo {
  cpu_cores: number | null;
  cpu_architecture: string | null;
  memory_size: number | null;
  gpu_vendor: string | null;
  gpu_renderer: string | null;
  screen_width: number | null;
  screen_height: number | null;
  screen_color_depth: number | null;
  pixel_ratio: number | null;
  platform: string | null;
  language: string | null;
  timezone: string | null;
  touch_support: boolean | null;
  cookie_enabled: boolean | null;
}

export class DeviceInfoCollector {
  static async collectDeviceInfo(): Promise<DeviceInfo> {
    const deviceInfo: DeviceInfo = {
      cpu_cores: null,
      cpu_architecture: null,
      memory_size: null,
      gpu_vendor: null,
      gpu_renderer: null,
      screen_width: null,
      screen_height: null,
      screen_color_depth: null,
      pixel_ratio: null,
      platform: null,
      language: null,
      timezone: null,
      touch_support: null,
      cookie_enabled: null
    };

    try {
      if ((navigator as any).hardwareConcurrency) {
        deviceInfo.cpu_cores = (navigator as any).hardwareConcurrency;
      }

      if (navigator.platform) {
        deviceInfo.platform = navigator.platform;
      }

      if (navigator.language) {
        deviceInfo.language = navigator.language;
      }

      try {
        deviceInfo.timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      } catch {}

      if (screen) {
        deviceInfo.screen_width = screen.width;
        deviceInfo.screen_height = screen.height;
        deviceInfo.screen_color_depth = screen.colorDepth;
      }

      if (window.devicePixelRatio) {
        deviceInfo.pixel_ratio = window.devicePixelRatio;
      }

      if (navigator.maxTouchPoints !== undefined) {
        deviceInfo.touch_support = navigator.maxTouchPoints > 0;
      }

      if (navigator.cookieEnabled !== undefined) {
        deviceInfo.cookie_enabled = navigator.cookieEnabled;
      }

      if ((navigator as any).deviceMemory) {
        deviceInfo.memory_size = (navigator as any).deviceMemory;
      }

      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (gl) {
          const debugInfo = (gl as any).getExtension('WEBGL_debug_renderer_info');
          if (debugInfo) {
            deviceInfo.gpu_vendor = (gl as any).getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
            deviceInfo.gpu_renderer = (gl as any).getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
          }
        }
      } catch {}

      try {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('x64') || userAgent.includes('Win64') || userAgent.includes('x86_64')) {
          deviceInfo.cpu_architecture = 'x64';
        } else if (userAgent.includes('ARM') || userAgent.includes('arm')) {
          deviceInfo.cpu_architecture = 'ARM';
        } else if (userAgent.includes('x86')) {
          deviceInfo.cpu_architecture = 'x86';
        }
      } catch {}

    } catch {}

    return deviceInfo;
  }

  static encryptDeviceInfo(deviceInfo: DeviceInfo): string {
    try {
      const components = [
        deviceInfo.cpu_cores?.toString() || '',
        deviceInfo.cpu_architecture || '',
        deviceInfo.memory_size?.toString() || '',
        deviceInfo.gpu_vendor || '',
        deviceInfo.gpu_renderer || '',
        deviceInfo.screen_width?.toString() || '',
        deviceInfo.screen_height?.toString() || '',
        deviceInfo.screen_color_depth?.toString() || '',
        deviceInfo.pixel_ratio?.toString() || '',
        deviceInfo.platform || '',
        deviceInfo.language || '',
        deviceInfo.timezone || '',
        deviceInfo.touch_support?.toString() || '',
        deviceInfo.cookie_enabled?.toString() || ''
      ];

      const dataString = components.join('|');
      const secretKey = 'simple-key-123';
      return CustomCrypto.encrypt(dataString, secretKey);
    } catch (error) {
      console.error('设备信息加密失败:', error);
      return '';
    }
  }
}
