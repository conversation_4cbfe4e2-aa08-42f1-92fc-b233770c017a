import type { NetworkSecurity } from '../types';

export class NetworkSecurityManager {
  private static lastCheck: { timestamp: number; result: NetworkSecurity } | null = null;
  private static readonly CACHE_DURATION = 30000;

  static async getUserNetworkInfo(): Promise<{ networkName: string; ipAddress: string } | null> {
    try {
      const response = await fetch('https://qifu-api.baidubce.com/ip/local/geo/v1/district');
      if (response.ok) {
        const result = await response.json();
        if (result.code === 'Success' && result.data && result.ip) {
          const data = result.data;
          const prov = data.prov || '';
          const city = data.city || '';
          const district = data.district || '';
          const isp = data.isp || '';
          const owner = data.owner || '';

          let location = '';
          if (prov && city && district) {
            location = `${prov}${city}${district}`;
          } else if (prov && city) {
            location = `${prov}${city}`;
          } else if (city) {
            location = city;
          } else if (prov) {
            location = prov;
          }

          const networkProvider = isp || owner || '';
          let networkName = '';
          if (networkProvider && location) {
            networkName = `${networkProvider} - ${location}`;
          } else if (networkProvider) {
            networkName = networkProvider;
          } else if (location) {
            networkName = location;
          } else {
            networkName = '未知网络';
          }

          return {
            networkName,
            ipAddress: result.ip
          };
        }
      }
    } catch {
    }
    return null;
  }

  static async checkNetworkSecurity(): Promise<NetworkSecurity> {
    try {
      const now = Date.now();

      if (this.lastCheck && (now - this.lastCheck.timestamp) < this.CACHE_DURATION) {
        return this.lastCheck.result;
      }

      const networkInfo = await this.getUserNetworkInfo();

      if (!networkInfo) {
        const result = {
          is_safe: true,
          current_network: null,
          message: '网络环境安全',
          restricted: false
        };

        this.lastCheck = { timestamp: now, result };
        return result;
      }

      const response = await fetch('/api/network/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          network_name: networkInfo.networkName,
          ip_address: networkInfo.ipAddress
        })
      });

      if (response.ok) {
        const data = await response.json();
        const verifyResult = data.data;

        const networkSecurity = {
          is_safe: verifyResult.is_safe,
          current_network: networkInfo.networkName,
          message: `用户网络: ${networkInfo.networkName}${verifyResult.restricted ? ' (受限制)' : ' (安全)'}`,
          restricted: verifyResult.restricted
        };

        this.lastCheck = { timestamp: now, result: networkSecurity };
        return networkSecurity;
      } else {
        throw new Error('网络验证API调用失败');
      }

    } catch {
      return {
        is_safe: true,
        current_network: null,
        message: '网络环境安全',
        restricted: false
      };
    }
  }

  static clearCache(): void {
    this.lastCheck = null;
  }

  static isNetworkRestricted(networkSecurity: NetworkSecurity): boolean {
    return !networkSecurity.is_safe || networkSecurity.restricted;
  }
}
