
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from './contexts/ThemeContext';
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import DouyinLoginPage from './pages/DouyinLoginPage';
import DouyinOALoginPage from './pages/DouyinOALoginPage';
import DashboardPage from './pages/DashboardPage';
import DouyinOADashboardPage from './pages/DouyinOADashboardPage';
import DouyinDeveloperWorkspacePage from './pages/DouyinDeveloperWorkspacePage';
import LiandongWorkspacePage from './pages/LiandongWorkspacePage';

// 创建QueryClient实例
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

function App() {
  return (
    <ThemeProvider>
      <QueryClientProvider client={queryClient}>
        <Router>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/login/douyin" element={<DouyinLoginPage />} />
            <Route path="/login/douyin-oa" element={<DouyinOALoginPage />} />
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/dashboard/oa" element={<DouyinOADashboardPage />} />
            <Route path="/workspace/douyin/developer" element={<DouyinDeveloperWorkspacePage />} />
            <Route path="/workspace/liandong" element={<LiandongWorkspacePage />} />
          </Routes>
        </Router>
      </QueryClientProvider>
    </ThemeProvider>
  );
}

export default App;
