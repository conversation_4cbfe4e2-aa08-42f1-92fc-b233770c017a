import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Radar, TrendingUp, Shield, Zap, Users, BarChart3 } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import WorkflowDiagram from '../components/WorkflowDiagram';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();

  const features = [
    {
      icon: TrendingUp,
      title: '数据洞察',
      description: '深度分析社交媒体数据，发现趋势和机会',
    },
    {
      icon: Shield,
      title: '安全可靠',
      description: '企业级安全保障，保护您的数据隐私',
    },
    {
      icon: Zap,
      title: '快速响应',
      description: '实时数据处理，快速获取分析结果',
    },
    {
      icon: Users,
      title: '多平台支持',
      description: '支持抖音、小红书等主流社交平台',
    },
    {
      icon: BarChart3,
      title: '可视化报告',
      description: '直观的数据可视化，让洞察一目了然',
    },
    {
      icon: Radar,
      title: '智能监控',
      description: '24/7智能监控，及时发现重要变化',
    },
  ];

  return (
    <Layout>
      <section className="relative px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="max-w-7xl mx-auto text-center">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-600 rounded-2xl mb-6">
              <Radar className="h-8 w-8 text-white" />
            </div>
            <h1 className={`text-4xl md:text-6xl font-bold mb-6 leading-tight transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              <span className="bg-gradient-to-r from-primary-400 to-blue-400 bg-clip-text text-transparent">
                ReachRadar
              </span>
              <br />
              社交媒体数据分析平台
            </h1>
            <p className={`text-xl mb-8 max-w-3xl mx-auto leading-relaxed transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              专业的社交媒体数据分析工具，帮助您深度洞察用户行为，
              发现商业机会，制定精准的营销策略
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              onClick={() => navigate('/login')}
              className="w-full sm:w-auto"
            >
              开始使用
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() => document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' })}
              className="w-full sm:w-auto"
            >
              了解更多
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className={`text-3xl md:text-4xl font-bold mb-4 transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              强大的功能特性
            </h2>
            <p className={`text-lg max-w-2xl mx-auto transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              我们提供全面的社交媒体数据分析解决方案，助力您的业务增长
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                variant={theme === 'dark' ? 'premium' : 'premium'}
                hover
                className="group transition-gpu"
              >
                <div className="flex flex-col items-center text-center relative z-10">
                  {/* 增强的图标容器 */}
                  <div className={`relative flex items-center justify-center w-16 h-16 rounded-2xl mb-6 transition-all duration-500 ${
                    theme === 'dark'
                      ? 'bg-gradient-to-br from-primary-500/20 to-blue-600/20 group-hover:from-primary-400/30 group-hover:to-blue-500/30 shadow-lg shadow-primary-500/20'
                      : 'bg-gradient-to-br from-primary-100 to-blue-100 group-hover:from-primary-200 group-hover:to-blue-200 shadow-lg shadow-primary-500/20'
                  }`}>
                    {/* 背景光晕效果 */}
                    <div className={`absolute inset-0 rounded-2xl transition-opacity duration-500 ${
                      theme === 'dark'
                        ? 'bg-gradient-to-br from-primary-400/10 to-blue-500/10 group-hover:opacity-100 opacity-60'
                        : 'bg-gradient-to-br from-primary-200/30 to-blue-200/30 group-hover:opacity-100 opacity-70'
                    }`} />
                    <feature.icon className={`relative z-10 h-8 w-8 transition-all duration-500 group-hover:scale-110 ${
                      theme === 'dark' ? 'text-primary-300 group-hover:text-primary-200' : 'text-primary-600 group-hover:text-primary-700'
                    }`} />
                  </div>

                  {/* 增强的标题 */}
                  <h3 className={`text-xl font-bold mb-3 transition-all duration-300 group-hover:scale-105 ${
                    theme === 'dark' ? 'text-white group-hover:text-primary-100' : 'text-gray-900 group-hover:text-primary-800'
                  }`}>
                    {feature.title}
                  </h3>

                  {/* 增强的描述文字 */}
                  <p className={`leading-relaxed transition-all duration-300 text-center max-w-xs ${
                    theme === 'dark' ? 'text-slate-300 group-hover:text-slate-200' : 'text-gray-600 group-hover:text-gray-700'
                  }`}>
                    {feature.description}
                  </p>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Workflow Section */}
      <section className="px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className={`text-3xl md:text-4xl font-bold mb-4 transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              智能协同架构
            </h2>
            <p className={`text-lg max-w-2xl mx-auto transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              体验我们的智能协同工作流程，高效处理复杂问题。
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <WorkflowDiagram />
          </div>




          {/* 流程说明 */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-primary-600/10 border border-primary-600/20">
              <div className="w-2 h-2 bg-primary-500 rounded-full animate-pulse" />
              <span className={`text-sm font-medium transition-colors duration-300 ${
                theme === 'dark' ? 'text-primary-300' : 'text-primary-700'
              }`}>
                智能协同，动态反馈，持续优化
              </span>
            </div>
          </div>
        </div>
      </section>

      <section className="px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-4xl mx-auto">
          <Card
            variant="premium"
            padding="lg"
            className="text-center transition-gpu"
            hover
          >
            <h2 className={`text-3xl md:text-4xl font-bold mb-4 transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              准备开始您的数据分析之旅？
            </h2>
            <p className={`text-lg mb-8 max-w-2xl mx-auto transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              立即登录，体验专业的社交媒体数据分析服务
            </p>
            <Button
              size="lg"
              onClick={() => navigate('/login')}
              className="mx-auto"
            >
              立即登录
            </Button>
          </Card>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;
