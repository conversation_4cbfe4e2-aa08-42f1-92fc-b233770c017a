import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, CheckCircle, XCircle, Clock, Radar, Video, Loader2 } from 'lucide-react';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import { useTheme } from '../hooks/useTheme';
import { douyinOAApi } from '../services/api';
import { MultiAccountManager } from '../utils/multiAccount';
import type { OAAuthUrlResponse, OAUserInfo, OATokenInfo } from '../types';

const DouyinOALoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchParams] = useSearchParams();
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [isProcessingCallback, setIsProcessingCallback] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [userInfo, setUserInfo] = useState<OAUserInfo | null>(null);

  const isAddMode = searchParams.get('mode') === 'add';

  const handleOAuthSuccess = useCallback(async (userData: any) => {
    try {
      setIsProcessingCallback(true);
      setError(null);

      const userInfo: OAUserInfo = {
        open_id: userData.open_id,
        union_id: '',
        nickname: userData.nickname,
        avatar: userData.avatar,
        gender: 0,
        city: '',
        province: '',
        country: ''
      };

      const tokenInfo: OATokenInfo = {
        access_token: '',
        refresh_token: '',
        expires_in: 7200,
        refresh_expires_in: 86400,
        scope: userData.scopes ? userData.scopes.split(',') : ['user_info'],
        open_id: userData.open_id,
        created_at: new Date().toISOString()
      };

      try {
        await MultiAccountManager.addOAAccount(tokenInfo, userInfo);
      } catch (addError) {
        console.warn('保存账号信息时出现警告:', addError);
      }

      setUserInfo(userInfo);
      setLoginSuccess(true);

    } catch (err) {
      setError(err instanceof Error ? err.message : '处理授权信息失败');
    } finally {
      setIsProcessingCallback(false);
    }
  }, []);

  useEffect(() => {
    const success = searchParams.get('success');
    const userDataParam = searchParams.get('user_data');
    const error = searchParams.get('error');

    if (error) {
      setError(decodeURIComponent(error));
      return;
    }

    if (success === 'true' && userDataParam) {
      try {

        const userData = JSON.parse(decodeURIComponent(userDataParam));
        handleOAuthSuccess(userData);
      } catch (err) {
        setError('用户数据解析失败');
      }
    }
  }, [searchParams, handleOAuthSuccess]);

  const startOALogin = async () => {
    try {
      setLoading(true);
      setError(null);

      const authResponse: OAAuthUrlResponse = await douyinOAApi.generateAuthUrl({
        scope: ["user_info"]
      });

      setAuthUrl(authResponse.auth_url);
      window.location.href = authResponse.auth_url;

    } catch (err) {
      setError(err instanceof Error ? err.message : '生成授权链接失败');
    } finally {
      setLoading(false);
    }
  };

  const goBack = () => {
    navigate('/login');
  };

  const goToOADashboard = () => {
    navigate('/dashboard/oa');
  };

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md space-y-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={goBack}
              className="flex items-center space-x-2 text-sm"
            >
              <ArrowLeft className="h-4 w-4" />
              <span>返回登录选择</span>
            </Button>
          </div>

          <div className="text-center space-y-4">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
              theme === 'dark'
                ? 'bg-gradient-to-r from-red-600 to-pink-500'
                : 'bg-gradient-to-r from-red-500 to-pink-600'
            } shadow-lg`}>
              <Radar className="h-8 w-8 text-white" />
            </div>

            <div>
              <h2 className={`text-3xl font-bold mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {isAddMode ? '添加抖音账号' : '抖音登录'}
              </h2>
              <p className={`text-lg ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {isAddMode ? '通过抖音官方授权添加账号' : '使用抖音官方授权登录'}
              </p>
            </div>
          </div>

          <Card variant="premium" className="text-center">
            {isProcessingCallback ? (
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-blue-500/20' : 'bg-blue-100'
                } shadow-lg`}>
                  <Loader2 className={`h-10 w-10 animate-spin ${
                    theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    正在处理授权
                  </h3>
                  <p className={`mb-6 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    正在验证授权信息并获取用户数据...
                  </p>
                </div>
              </div>
            ) : loginSuccess ? (
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-green-500/20' : 'bg-green-100'
                } shadow-lg`}>
                  <CheckCircle className={`h-10 w-10 ${
                    theme === 'dark' ? 'text-green-400' : 'text-green-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {isAddMode ? '账号添加成功' : '登录成功'}
                  </h3>
                  {userInfo && (
                    <div className="space-y-2">
                      <p className={`${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`}>
                        欢迎，{userInfo.nickname}！
                      </p>
                      {userInfo.avatar && (
                        <img 
                          src={userInfo.avatar} 
                          alt="头像" 
                          className="w-12 h-12 rounded-full mx-auto"
                        />
                      )}
                    </div>
                  )}
                </div>
                <Button
                  onClick={goToOADashboard}
                  className="w-full"
                >
                  进入抖音仪表板
                </Button>
              </div>
            ) : error ? (
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100'
                } shadow-lg`}>
                  <XCircle className={`h-10 w-10 ${
                    theme === 'dark' ? 'text-red-400' : 'text-red-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    授权失败
                  </h3>
                  <p className={`mb-6 text-sm ${
                    theme === 'dark' ? 'text-red-400' : 'text-red-600'
                  }`}>
                    {error}
                  </p>
                </div>
                <Button
                  onClick={startOALogin}
                  loading={loading}
                  disabled={loading}
                  className="w-full"
                >
                  重新授权
                </Button>
              </div>
            ) : authUrl ? (
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-blue-500/20' : 'bg-blue-100'
                } shadow-lg`}>
                  <Clock className={`h-10 w-10 ${
                    theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    等待授权完成
                  </h3>
                  <p className={`mb-6 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    正在跳转到抖音授权页面，请稍候...
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100'
                } shadow-lg`}>
                  <Video className={`h-10 w-10 ${
                    theme === 'dark' ? 'text-red-400' : 'text-red-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {isAddMode ? '准备添加官方账号' : '准备官方授权登录'}
                  </h3>
                  <p className={`mb-6 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    点击下方按钮开始抖音官方OAuth2.0授权，将跳转到授权页面
                  </p>
                </div>
                <Button
                  onClick={startOALogin}
                  loading={loading}
                  disabled={loading}
                  className="w-full"
                >
                  {isAddMode ? '开始官方授权添加' : '开始官方授权登录'}
                </Button>
              </div>
            )}
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default DouyinOALoginPage;
