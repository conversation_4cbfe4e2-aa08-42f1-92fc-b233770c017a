import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Radar, Video, Camera } from 'lucide-react';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import Loading from '../components/Loading';
import { useTheme } from '../hooks/useTheme';
import { checkAnyDouyinLoginStatus } from '../utils/auth';
import type { LoginPlatform } from '../types';

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [selectedPlatform, setSelectedPlatform] = useState<LoginPlatform | null>(null);
  const [isCheckingLogin, setIsCheckingLogin] = useState(false);
  const [loginCheckMessage, setLoginCheckMessage] = useState('');

  const platforms = [
    {
      id: 'douyin' as LoginPlatform,
      name: '抖音',
      description: '选择抖音登录方式',
      icon: Video,
      color: 'from-red-500 to-pink-500',
      available: true,
      loginMethods: [
        {
          id: 'douyin-nodriver',
          name: '开发者登录',
          description: '通过浏览器扫码登录（推荐）',
          route: '/login/douyin'
        },
        {
          id: 'douyin-oa',
          name: '抖音登录',
          description: '通过抖音官方OAuth2.0授权',
          route: '/login/douyin-oa'
        }
      ]
    },
    {
      id: 'xiaohongshu' as LoginPlatform,
      name: '小红书',
      description: '使用小红书账号登录',
      icon: Camera,
      color: 'from-pink-500 to-red-500',
      available: false, // 暂时不可用
    },
  ];

  const handlePlatformSelect = (platform: LoginPlatform) => {
    if (platform === 'douyin') {
      setSelectedPlatform(platform);
    } else {
      // 小红书暂时不可用
      setShowComingSoon(true);
      setTimeout(() => setShowComingSoon(false), 3000);
    }
  };

  const handleLoginMethodSelect = async (route: string) => {
    // 如果是抖音官方登录，先检查是否已经登录
    if (route === '/login/douyin-oa') {
      try {
        setIsCheckingLogin(true);
        setLoginCheckMessage('正在检查登录状态...');

        const loginStatus = await checkAnyDouyinLoginStatus();

        if (loginStatus.hasLogin && loginStatus.loginType === 'oa') {
          setLoginCheckMessage('检测到官方授权登录，正在跳转...');
          setTimeout(() => {
            navigate('/dashboard/oa');
          }, 1000);
          return;
        }

        setIsCheckingLogin(false);
        navigate(route);

      } catch (error) {
        console.error('检查登录状态失败:', error);
        setIsCheckingLogin(false);
        navigate(route);
      }
    } else {
      navigate(route);
    }
  };

  const handleBackToPlatforms = () => {
    setSelectedPlatform(null);
  };





  if (isCheckingLogin) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <Loading size="lg" text={loginCheckMessage} />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* 背景装饰 */}
      <div className={`fixed inset-0 ${
        theme === 'dark'
          ? 'bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900'
          : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
      }`}>
        <div className={`absolute inset-0 ${
          theme === 'dark'
            ? 'bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary-900/20 via-transparent to-transparent'
            : 'bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary-100/30 via-transparent to-transparent'
        }`} />
      </div>

      <div className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* 返回按钮 */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate('/')}
              icon={ArrowLeft}
              className={`${
                theme === 'dark'
                  ? 'text-dark-300 hover:text-white'
                  : 'text-gray-600 hover:text-gray-900'
              } transition-colors duration-200`}
            >
              返回首页
            </Button>
          </div>

          {/* 标题区域 */}
          <div className="text-center space-y-4">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
              theme === 'dark'
                ? 'bg-gradient-to-r from-primary-600 to-primary-500'
                : 'bg-gradient-to-r from-primary-500 to-primary-600'
            } shadow-lg`}>
              <Radar className="h-8 w-8 text-white" />
            </div>

            <div>
              <h2 className={`text-3xl font-bold mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {selectedPlatform ? '选择登录方式' : '选择登录平台'}
              </h2>
              <p className={`text-lg ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {selectedPlatform
                  ? '请选择您偏好的抖音登录方式'
                  : '请选择您要使用的社交媒体平台账号登录'
                }
              </p>
            </div>
          </div>

          {/* 平台选择或登录方式选择 */}
          <div className="space-y-4">
            {selectedPlatform ? (
              // 显示选中平台的登录方式
              <>
                {/* 返回按钮 */}
                <div className="flex items-center mb-4">
                  <Button
                    variant="ghost"
                    onClick={handleBackToPlatforms}
                    className="flex items-center space-x-2 text-sm"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    <span>返回平台选择</span>
                  </Button>
                </div>

                {/* 登录方式选择 */}
                {platforms.find(p => p.id === selectedPlatform)?.loginMethods?.map((method) => (
                  <Card
                    key={method.id}
                    variant="premium"
                    hover={true}
                    className={`cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${
                      theme === 'dark'
                        ? 'hover:border-primary-500/50 hover:shadow-primary-500/20'
                        : 'hover:border-primary-400/50 hover:shadow-primary-400/20'
                    } hover:shadow-xl`}
                    onClick={() => handleLoginMethodSelect(method.route)}
                  >
                    <div className="flex items-center space-x-4 p-2">
                      <div className={`flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-r ${
                        platforms.find(p => p.id === selectedPlatform)?.color
                      } shadow-lg`}>
                        <Video className="h-7 w-7 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className={`text-lg font-semibold ${
                          theme === 'dark' ? 'text-white' : 'text-gray-900'
                        }`}>
                          {method.name}
                        </h3>
                        <p className={`text-sm mt-1 ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          {method.description}
                        </p>
                      </div>
                      <div className={`${
                        theme === 'dark' ? 'text-primary-400' : 'text-primary-500'
                      } transition-transform duration-200 group-hover:translate-x-1`}>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Card>
                ))}
              </>
            ) : (
              // 显示平台选择
              platforms.map((platform) => (
                <Card
                  key={platform.id}
                  variant="premium"
                  hover={platform.available}
                  className={`cursor-pointer transition-all duration-300 transform ${
                    platform.available
                      ? `hover:scale-[1.02] ${
                          theme === 'dark'
                            ? 'hover:border-primary-500/50 hover:shadow-primary-500/20'
                            : 'hover:border-primary-400/50 hover:shadow-primary-400/20'
                        } hover:shadow-xl`
                      : 'opacity-60 cursor-not-allowed'
                  }`}
                  onClick={() => platform.available && handlePlatformSelect(platform.id)}
                >
                  <div className="flex items-center space-x-4 p-2">
                    <div className={`flex items-center justify-center w-14 h-14 rounded-2xl bg-gradient-to-r ${platform.color} shadow-lg`}>
                      <platform.icon className="h-7 w-7 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className={`text-lg font-semibold flex items-center ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {platform.name}
                        {!platform.available && (
                          <span className={`ml-2 text-xs px-2 py-1 rounded-full ${
                            theme === 'dark'
                              ? 'bg-dark-600 text-dark-300'
                              : 'bg-gray-100 text-gray-500'
                          }`}>
                            即将上线
                          </span>
                        )}
                      </h3>
                      <p className={`text-sm mt-1 ${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`}>
                        {platform.description}
                      </p>
                    </div>
                    {platform.available && (
                      <div className={`${
                        theme === 'dark' ? 'text-primary-400' : 'text-primary-500'
                      } transition-transform duration-200 group-hover:translate-x-1`}>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    )}
                  </div>
                </Card>
              ))
            )}
          </div>

          {/* 说明文字 */}
          <div className="text-center space-y-3">
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${
              theme === 'dark'
                ? 'bg-dark-800/50 border border-dark-700'
                : 'bg-white/80 border border-gray-200'
            } backdrop-blur-sm`}>
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
              <p className={`text-sm font-medium ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-700'
              }`}>
                登录后您可以使用ReachRadar的所有功能
              </p>
            </div>

            <p className={`text-xs ${
              theme === 'dark' ? 'text-dark-500' : 'text-gray-500'
            }`}>
              我们承诺保护您的隐私和数据安全
            </p>
          </div>
        </div>
      </div>

      {/* 即将上线提示 */}
      {showComingSoon && (
        <div className="fixed top-4 right-4 z-50">
          <Card className="bg-blue-500/10 border-blue-500/50">
            <div className="flex items-center space-x-3 p-4">
              <div className="text-blue-400">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <span className="text-blue-300 text-sm font-medium">
                小红书登录功能即将上线，敬请期待！
              </span>
            </div>
          </Card>
        </div>
      )}
    </Layout>
  );
};

export default LoginPage;
