import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Settings, LogOut, Shield, Video, BarChart3 } from 'lucide-react';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import { useTheme } from '../hooks/useTheme';
import { MultiAccountManager } from '../utils/multiAccount';
import { checkDouyinOALoginStatus } from '../utils/auth';
import type { DouyinOAAccount, OAUserInfo } from '../types';

const DouyinOADashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [currentAccount, setCurrentAccount] = useState<DouyinOAAccount | null>(null);
  const [loading, setLoading] = useState(true);
  const [userInfo, setUserInfo] = useState<OAUserInfo | null>(null);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    try {
      setLoading(true);
      const loginStatus = await checkDouyinOALoginStatus(true);
      
      if (!loginStatus.isLoggedIn) {
        navigate('/login');
        return;
      }

      const account = MultiAccountManager.getCurrentOAAccount();
      setCurrentAccount(account);
      setUserInfo(loginStatus.userInfo || null);
    } catch (error) {
      console.error('检查登录状态失败:', error);
      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    if (currentAccount) {
      MultiAccountManager.removeOAAccount(currentAccount.id);
    }
    navigate('/login');
  };



  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
            <p className={theme === 'dark' ? 'text-dark-300' : 'text-gray-600'}>
              正在加载...
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen p-4">
        <div className="max-w-6xl mx-auto space-y-6">
          <div className="text-center space-y-4">
            <h1 className={`text-3xl font-bold ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              抖音仪表板
            </h1>
            <p className={`text-lg ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              通过抖音官方API管理您的内容和数据
            </p>
          </div>

          <Card variant="premium" className="text-center">
            <div className="flex items-center space-x-4 p-4">
              {userInfo?.avatar ? (
                <img 
                  src={userInfo.avatar} 
                  alt="头像" 
                  className="w-16 h-16 rounded-full"
                />
              ) : (
                <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
                  theme === 'dark' ? 'bg-dark-600' : 'bg-gray-200'
                }`}>
                  <User className={`h-8 w-8 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
                  }`} />
                </div>
              )}
              
              <div className="flex-1 text-left">
                <h3 className={`text-xl font-semibold ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {userInfo?.nickname || '开发者用户'}
                </h3>
                <p className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  Open ID: {currentAccount?.open_id}
                </p>
                {userInfo?.city && userInfo?.province && (
                  <p className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    位置: {userInfo.province} {userInfo.city}
                  </p>
                )}
              </div>

              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={handleLogout}
                  className="flex items-center space-x-2 text-red-600 hover:text-red-700"
                >
                  <LogOut className="h-4 w-4" />
                  <span>退出登录</span>
                </Button>
              </div>
            </div>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card variant="premium" hover className="cursor-pointer">
              <div className="p-6 text-center space-y-4">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
                  theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100'
                } shadow-lg`}>
                  <Video className={`h-8 w-8 ${
                    theme === 'dark' ? 'text-red-400' : 'text-red-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    视频管理
                  </h3>
                  <p className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    查看和管理您的抖音视频内容
                  </p>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  theme === 'dark' 
                    ? 'bg-yellow-500/20 text-yellow-400' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  即将上线
                </div>
              </div>
            </Card>

            <Card variant="premium" hover className="cursor-pointer">
              <div className="p-6 text-center space-y-4">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
                  theme === 'dark' ? 'bg-blue-500/20' : 'bg-blue-100'
                } shadow-lg`}>
                  <BarChart3 className={`h-8 w-8 ${
                    theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    数据分析
                  </h3>
                  <p className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    查看粉丝数据和视频表现分析
                  </p>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  theme === 'dark' 
                    ? 'bg-yellow-500/20 text-yellow-400' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  即将上线
                </div>
              </div>
            </Card>

            <Card variant="premium" hover className="cursor-pointer">
              <div className="p-6 text-center space-y-4">
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
                  theme === 'dark' ? 'bg-green-500/20' : 'bg-green-100'
                } shadow-lg`}>
                  <Settings className={`h-8 w-8 ${
                    theme === 'dark' ? 'text-green-400' : 'text-green-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    账号设置
                  </h3>
                  <p className={`text-sm ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    管理授权权限和账号配置
                  </p>
                </div>
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                  theme === 'dark' 
                    ? 'bg-yellow-500/20 text-yellow-400' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  即将上线
                </div>
              </div>
            </Card>
          </div>

          <Card className="text-center">
            <div className="p-6 space-y-4">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${
                theme === 'dark' ? 'bg-primary-500/20' : 'bg-primary-100'
              }`}>
                <Shield className={`h-6 w-6 ${
                  theme === 'dark' ? 'text-primary-400' : 'text-primary-600'
                }`} />
              </div>
              <div>
                <h3 className={`text-lg font-semibold mb-2 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  官方授权系统
                </h3>
                <p className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  您正在使用抖音官方OAuth2.0授权系统，享受更安全、更稳定的API服务。
                  此系统与扫码登录系统完全独立，提供不同的功能和权限。
                </p>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default DouyinOADashboardPage;
