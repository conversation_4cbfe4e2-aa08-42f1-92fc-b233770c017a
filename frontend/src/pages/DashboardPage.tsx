import React, { useState, useEffect } from 'react';
import { ArrowRight, Shield, Copy, CheckCircle, AlertTriangle, Monitor, Smartphone, Crown } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import Card from '../components/Card';
import Button from '../components/Button';
import AccountManager from '../components/AccountManager';
import CookieImportComponent from '../components/CookieImportComponent';
import { deviceApi } from '../services/api';
import { NetworkSecurityManager } from '../utils/networkSecurity';
import { DeviceInfoCollector } from '../utils/deviceInfo';

import type { DeviceVerificationResponse, NetworkSecurity } from '../types';

const DashboardPage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [deviceVerification, setDeviceVerification] = useState<DeviceVerificationResponse | null>(null);
  const [deviceFingerprint, setDeviceFingerprint] = useState<string>('');
  const [copySuccess, setCopySuccess] = useState(false);
  const [networkSecurity, setNetworkSecurity] = useState<NetworkSecurity | null>(null);
  const [showNetworkWarning, setShowNetworkWarning] = useState(false);

  const verifyDevice = async () => {
    try {
      const deviceInfo = await DeviceInfoCollector.collectDeviceInfo();
      const encryptedDeviceInfo = DeviceInfoCollector.encryptDeviceInfo(deviceInfo);

      const verificationResult = await deviceApi.verify({
        encrypted_device_info: encryptedDeviceInfo
      });

      setDeviceVerification(verificationResult);
      if (verificationResult.device_fingerprint) {
        setDeviceFingerprint(verificationResult.device_fingerprint);
      }
    } catch {
      // 静默处理错误
    }
  };

  const copyDeviceFingerprint = async () => {
    if (deviceFingerprint) {
      try {
        await navigator.clipboard.writeText(deviceFingerprint);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch {
        const textArea = document.createElement('textarea');
        textArea.value = deviceFingerprint;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        } catch {
        } finally {
          document.body.removeChild(textArea);
        }
      }
    }
  };

  const checkNetworkSecurity = async () => {
    try {
      NetworkSecurityManager.clearCache();
      const security = await NetworkSecurityManager.checkNetworkSecurity();
      setNetworkSecurity(security);

      if (NetworkSecurityManager.isNetworkRestricted(security)) {
        setShowNetworkWarning(true);
      } else {
        setShowNetworkWarning(false);
      }
    } catch (error) {
      setShowNetworkWarning(false);
    }
  };

  useEffect(() => {
    verifyDevice();
    checkNetworkSecurity();
  }, []);

  return (
    <Layout>
      <div className="min-h-screen px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-7xl mx-auto">
          {showNetworkWarning && networkSecurity && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-500" />
                <div>
                  <h3 className="text-red-800 font-medium">网络安全警告</h3>
                  <p className="text-red-700 text-sm mt-1">{networkSecurity.message}</p>
                  <p className="text-red-600 text-sm mt-1">请切换到安全网络后重试操作</p>
                </div>
              </div>
            </div>
          )}

          <div className="mb-8">
            <div>
              <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                ReachRadar 仪表盘
              </h1>
              <p className={`text-lg transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                欢迎使用ReachRadar数据分析平台
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <Card variant="glass">
              <div className="flex items-center space-x-4">
                <div className={`flex items-center justify-center w-12 h-12 rounded-xl ${
                  deviceVerification?.is_developer ? 'bg-green-500/20' : 'bg-orange-500/20'
                }`}>
                  <Shield className={`h-6 w-6 ${
                    deviceVerification?.is_developer ? 'text-green-400' : 'text-orange-400'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>开发者身份</h3>
                  <p className={deviceVerification?.is_developer ? 'text-green-400' : 'text-orange-400'}>
                    {deviceVerification?.is_developer ? '已验证' : '未验证'}
                  </p>
                </div>
              </div>
            </Card>

            <Card variant="glass">
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-12 h-12 bg-blue-500/20 rounded-xl">
                    <Copy className="h-6 w-6 text-blue-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>设备信息</h3>
                    <div className={`text-sm transition-colors duration-300 flex items-center space-x-2 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
                    }`}>
                      {(() => {
                        const userAgent = navigator.userAgent;
                        if (userAgent.includes('Mac')) {
                          return (
                            <>
                              <Monitor className="h-4 w-4" />
                              <span>macOS</span>
                            </>
                          );
                        } else if (userAgent.includes('Windows')) {
                          return (
                            <>
                              <Monitor className="h-4 w-4" />
                              <span>Windows</span>
                            </>
                          );
                        } else if (userAgent.includes('Linux')) {
                          return (
                            <>
                              <Monitor className="h-4 w-4" />
                              <span>Linux</span>
                            </>
                          );
                        } else if (userAgent.includes('Android') || userAgent.includes('iPhone') || userAgent.includes('iPad')) {
                          return (
                            <>
                              <Smartphone className="h-4 w-4" />
                              <span>移动设备</span>
                            </>
                          );
                        } else {
                          return (
                            <>
                              <Monitor className="h-4 w-4" />
                              <span>未知系统</span>
                            </>
                          );
                        }
                      })()}
                    </div>
                  </div>
                </div>

                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium transition-colors duration-300 ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>网络状态</span>
                    <div className="flex items-center space-x-2">
                      {networkSecurity ? (
                        <>
                          <div className={`w-2 h-2 rounded-full ${
                            networkSecurity.is_safe ? 'bg-green-500' : 'bg-red-500'
                          }`}></div>
                          <span className={`text-sm transition-colors duration-300 ${
                            networkSecurity.is_safe
                              ? (theme === 'dark' ? 'text-green-400' : 'text-green-600')
                              : (theme === 'dark' ? 'text-red-400' : 'text-red-600')
                          }`}>
                            {networkSecurity.current_network || '未知网络'}
                          </span>
                        </>
                      ) : (
                        <span className={`text-sm transition-colors duration-300 ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
                        }`}>检测中...</span>
                      )}
                    </div>
                  </div>
                </div>

                {deviceFingerprint && (
                  <div className="border-t pt-3">
                    <div className="flex items-center justify-between">
                      <span className={`text-sm font-medium transition-colors duration-300 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>设备指纹</span>
                      <Button
                        onClick={copyDeviceFingerprint}
                        variant="outline"
                        size="sm"
                        className={`shrink-0 transition-colors duration-200 ${
                          copySuccess ? 'bg-green-500/20 border-green-500/50' : ''
                        }`}
                      >
                        {copySuccess ? (
                          <CheckCircle className="h-4 w-4 text-green-400" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <p className={`text-xs font-mono mt-1 truncate transition-colors duration-300 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
                    }`}>{deviceFingerprint}</p>
                  </div>
                )}
              </div>
            </Card>
          </div>

          <AccountManager
            isDeveloper={deviceVerification?.is_developer || false}
          />

          <CookieImportComponent isDeveloper={deviceVerification?.is_developer || false} />

          <Card variant="gradient" padding="lg">
            <div className="text-center">
              <h2 className={`text-2xl font-bold mb-4 transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                🎉 欢迎使用ReachRadar！
              </h2>
              <p className={`mb-6 max-w-2xl mx-auto transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                您可以通过上方的账号管理功能添加抖音账号，然后开始使用我们的数据分析功能。
              </p>
              <div className={`rounded-lg p-6 text-left transition-colors duration-300 ${
                theme === 'dark' ? 'bg-dark-800/50' : 'bg-gray-100/50'
              }`}>
                <h3 className={`text-lg font-semibold mb-3 transition-colors duration-300 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>平台功能：</h3>
                <ul className={`space-y-2 transition-colors duration-300 ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>多账号管理</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>数据分析</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>内容监控</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-400" />
                    <span>专业报告</span>
                  </li>
                </ul>
              </div>

              <div className="mt-6 text-center space-y-3">
                {deviceVerification?.is_developer ? (
                  <>
                    <Button
                      onClick={() => navigate('/workspace/douyin/developer')}
                      className="bg-purple-500 hover:bg-purple-600 flex items-center space-x-2 mx-auto"
                    >
                      <Crown className="h-4 w-4" />
                      <span>开发者工作台</span>
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                    <div className="text-xs text-center">
                      <Button
                        onClick={() => navigate('/workspace/liandong')}
                        variant="outline"
                        className="text-sm bg-orange-500 hover:bg-orange-600 text-white border-orange-500"
                      >
                        联东工作台
                      </Button>
                    </div>
                  </>
                ) : (
                  <div className="text-center">
                    <p className={`text-sm mb-4 transition-colors duration-300 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                    }`}>
                      请联系管理员获取开发者权限以使用工作台功能
                    </p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default DashboardPage;
