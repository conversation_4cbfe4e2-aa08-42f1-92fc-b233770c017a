import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Building2, AlertTriangle } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import TopicBatchSearchComponent from '../components/TopicBatchSearchComponent.tsx';
import CommentAnalysisComponent from '../components/CommentAnalysisComponent';
import CustomerAnalysisComponent from '../components/CustomerAnalysisComponent';
import BatchUserSignatureComponent from '../components/BatchUserSignatureComponent';
import RealEstateAnalysisComponent from '../components/RealEstateAnalysisComponent';
import { MultiAccountManager, type DouyinAccount } from '../utils/multiAccount';
import type { CommentResult, SearchResponse, RealEstateAnalysisResponse, CommentCustomerAnalysisResult } from '../types';

const LiandongWorkspacePage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();

  const getThemeClasses = (lightClass: string, darkClass: string) =>
    theme === 'dark' ? darkClass : lightClass;

  const [commentResult, setCommentResult] = useState<CommentResult | null>(null);
  const [commentAccounts, setCommentAccounts] = useState<DouyinAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<DouyinAccount | null>(null);
  const [showAnalysisModal, setShowAnalysisModal] = useState(false);
  const [analysisRequired, setAnalysisRequired] = useState(false);
  const [analysisCompleted, setAnalysisCompleted] = useState(false);
  const [lastSearchResult, setLastSearchResult] = useState<SearchResponse | null>(null);
  const [realEstateAnalysisResult, setRealEstateAnalysisResult] = useState<RealEstateAnalysisResponse | null>(null);
  const [customerAnalysisResult, setCustomerAnalysisResult] = useState<CommentCustomerAnalysisResult | null>(null);

  useEffect(() => {
    const loadCommentAccounts = () => {
      const allAccounts = MultiAccountManager.getAccounts();
      const validAccounts = allAccounts.filter(account => account.isValid);
      setCommentAccounts(validAccounts);
      if (validAccounts.length > 0) {
        setSelectedAccount(validAccounts[0]);
      }
    };

    loadCommentAccounts();
  }, []);

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleSearchComplete = (result: SearchResponse) => {
    setLastSearchResult(result);

    // 检查是否有数据写入数据库
    const databaseStats = result?.database_stats;
    if (databaseStats && databaseStats.inserted_count > 0) {
      // 有新数据写入数据库，需要进行房地产分析
      setAnalysisRequired(true);
      setAnalysisCompleted(false);
      setShowAnalysisModal(true);
    }
  };

  const handleAnalysisComplete = (result?: RealEstateAnalysisResponse) => {
    setAnalysisCompleted(true);
    setAnalysisRequired(false);
    if (result) {
      setRealEstateAnalysisResult(result);
    }
    // 不自动关闭模态框，让用户可以查看结果和导出
  };

  const handleCloseAnalysisModal = () => {
    setShowAnalysisModal(false);
  };

  const handleCommentAnalysisComplete = (result: CommentResult) => {
    setCommentResult(result);
  };

  return (
    <Layout>
      <div className="min-h-screen py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleBackToDashboard}
                className="bg-gray-500 hover:bg-gray-600 flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回仪表盘</span>
              </Button>
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-10 h-10 bg-orange-500 rounded-lg">
                  <Building2 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className={`text-3xl font-bold transition-colors duration-300 ${
                    getThemeClasses('text-gray-900', 'text-white')
                  }`}>
                    联东工作台
                  </h1>
                  <p className={`text-sm transition-colors duration-300 ${
                    getThemeClasses('text-gray-600', 'text-dark-300')
                  }`}>
                    批量话题搜索和数据分析
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className={analysisRequired && !analysisCompleted ? 'opacity-50 pointer-events-none' : ''}>
            <TopicBatchSearchComponent
              onSearchComplete={handleSearchComplete}
            />
          </div>

          <div className="mt-8">
            {/* 只有当房地产分析完成且有相关视频时才显示获取评论账号配置 */}
            {realEstateAnalysisResult && realEstateAnalysisResult.results.some(result => result.is_real_estate_related) && commentAccounts.length > 0 && (
                      <Card variant="glass" className="mb-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className={`font-medium ${
                              theme === 'dark' ? 'text-white' : 'text-gray-900'
                            }`}>
                              获取评论账号
                            </h4>
                            <p className={`text-sm ${
                              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                            }`}>
                              选择用于获取评论的账号
                            </p>
                          </div>
                          <select
                            value={selectedAccount?.id || ''}
                            onChange={(e) => {
                              const account = commentAccounts.find(acc => acc.id === e.target.value);
                              setSelectedAccount(account || null);
                            }}
                            className={`px-3 py-2 rounded-lg border text-sm ${
                              theme === 'dark'
                                ? 'bg-dark-800 border-dark-600 text-white'
                                : 'bg-white border-gray-300 text-gray-900'
                            }`}
                          >
                            {commentAccounts.map(account => (
                              <option key={account.id} value={account.id}>
                                {account.nickname}
                              </option>
                            ))}
                          </select>
                        </div>
              </Card>
            )}

            {/* 只有当房地产分析完成且有相关视频时才显示获取评论卡片 */}
            {realEstateAnalysisResult && realEstateAnalysisResult.results.some(result => result.is_real_estate_related) && (
              <div className={analysisRequired && !analysisCompleted ? 'opacity-50 pointer-events-none' : ''}>
                {selectedAccount ? (
                  <CommentAnalysisComponent
                    currentAccount={selectedAccount}
                    realEstateAnalysisResult={realEstateAnalysisResult}
                    onAnalysisComplete={handleCommentAnalysisComplete}
                    showCommentDetails={false}
                  />
                ) : (
                <Card variant="glass">
                  <div className="text-center py-8">
                    <div className={`text-lg font-medium mb-2 transition-colors duration-300 ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      暂无可用的评论账号
                    </div>
                    <p className={`text-sm transition-colors duration-300 ${
                      theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                    }`}>
                      获取评论需要有效的抖音账号Cookie，请先在开发者工作台添加账号
                    </p>
                    <Button
                      onClick={() => navigate('/workspace/douyin/developer')}
                      className="mt-4 bg-purple-500 hover:bg-purple-600"
                    >
                      前往开发者工作台
                    </Button>
                  </div>
                </Card>
                )}
              </div>
            )}

            <div className={analysisRequired && !analysisCompleted ? 'opacity-50 pointer-events-none' : ''}>
              {commentResult && (
                <div className="mt-8">
                  <CustomerAnalysisComponent
                    commentResult={commentResult}
                    showCustomerDetails={false}
                    onAnalysisComplete={setCustomerAnalysisResult}
                  />
                </div>
              )}
            </div>

            {/* 批量获取用户签名组件 */}
            <div className={analysisRequired && !analysisCompleted ? 'opacity-50 pointer-events-none' : ''}>
              {customerAnalysisResult && (
                <div className="mt-8">
                  <BatchUserSignatureComponent
                    customerAnalysisResult={customerAnalysisResult}
                    selectedAccount={selectedAccount}
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 强制房地产分析模态框 */}
        {showAnalysisModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`max-w-4xl w-full max-h-[90vh] overflow-y-auto rounded-lg shadow-xl ${
              getThemeClasses('bg-white', 'bg-dark-800')
            }`}>
              <div className={`sticky top-0 flex items-center justify-between p-6 border-b ${
                getThemeClasses('border-gray-200 bg-white', 'border-dark-600 bg-dark-800')
              }`}>
                <div className="flex items-center space-x-3">
                  <AlertTriangle className="w-6 h-6 text-orange-500" />
                  <h2 className={`text-xl font-semibold ${
                    getThemeClasses('text-gray-900', 'text-white')
                  }`}>
                    {analysisCompleted ? '房地产视频分析完成' : '必须进行房地产视频分析'}
                  </h2>
                </div>
                <div className="flex items-center space-x-4">
                  <div className={`text-sm ${
                    getThemeClasses('text-gray-600', 'text-dark-300')
                  }`}>
                    {analysisCompleted ? '分析已完成，可以导出结果' : '检测到新的视频数据，必须完成分析后才能继续'}
                  </div>
                  {analysisCompleted && (
                    <Button
                      onClick={handleCloseAnalysisModal}
                      variant="outline"
                      size="sm"
                    >
                      关闭
                    </Button>
                  )}
                </div>
              </div>

              <div className="p-6">
                {lastSearchResult?.database_stats && (
                  <div className={`mb-6 p-4 rounded-lg ${
                    getThemeClasses('bg-blue-50 border border-blue-200', 'bg-blue-900/20 border border-blue-700')
                  }`}>
                    <h3 className={`font-medium mb-2 ${
                      getThemeClasses('text-blue-900', 'text-blue-300')
                    }`}>
                      搜索结果统计
                    </h3>
                    <div className={`text-sm space-y-1 ${
                      getThemeClasses('text-blue-800', 'text-blue-400')
                    }`}>
                      <p>新增视频: {lastSearchResult.database_stats.inserted_count} 条</p>
                      <p>跳过重复: {lastSearchResult.database_stats.skipped_count} 条</p>
                      {lastSearchResult.database_stats.error_count > 0 && (
                        <p>处理错误: {lastSearchResult.database_stats.error_count} 条</p>
                      )}
                    </div>
                  </div>
                )}

                <RealEstateAnalysisComponent
                  searchResult={lastSearchResult}
                  onAnalysisComplete={handleAnalysisComplete}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default LiandongWorkspacePage;
