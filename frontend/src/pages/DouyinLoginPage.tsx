import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ArrowLeft, CheckCircle, XCircle, Clock, Radar, Video } from 'lucide-react';
import Layout from '../components/Layout';
import Button from '../components/Button';
import Card from '../components/Card';
import Loading from '../components/Loading';
import { useTheme } from '../hooks/useTheme';
import { douyinApi } from '../services/api';
import { checkDouyinLoginStatus } from '../utils/auth';
import { MultiAccountManager } from '../utils/multiAccount';
import { LoginStatus } from '../types';
import type { LoginSession } from '../types';

const DouyinLoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { theme } = useTheme();
  const [searchParams] = useSearchParams();
  const [session, setSession] = useState<LoginSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isCheckingLogin, setIsCheckingLogin] = useState(true);
  const [loginCheckMessage, setLoginCheckMessage] = useState('正在检查登录状态...');

  const isAddMode = searchParams.get('mode') === 'add';

  const checkLoginStatus = useCallback(async () => {
    if (isAddMode) {
      setIsCheckingLogin(false);
      return;
    }

    try {
      setIsCheckingLogin(true);
      setLoginCheckMessage('正在检查登录状态...');

      const loginStatus = await checkDouyinLoginStatus();

      if (loginStatus.isLoggedIn) {
        setLoginCheckMessage('检测到有效登录，正在跳转...');
        setTimeout(() => {
          navigate('/dashboard');
        }, 1000);
        return;
      }

      setIsCheckingLogin(false);

    } catch {
      setLoginCheckMessage('检查登录状态失败，请重新登录');
      setTimeout(() => {
        setIsCheckingLogin(false);
      }, 2000);
    }
  }, [isAddMode, navigate]);

  useEffect(() => {
    checkLoginStatus();
  }, [checkLoginStatus]);

  const startLogin = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const newSession = await douyinApi.startLogin({
        timeout_minutes: 10,
        headless: false,
      });
      
      setSession(newSession);
      
      // 开始轮询状态
      startStatusPolling(newSession.session_id);
    } catch (err) {
      setError(err instanceof Error ? err.message : '启动登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 轮询登录状态
  const startStatusPolling = (sessionId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const updatedSession = await douyinApi.getLoginStatus(sessionId);
        setSession(updatedSession);
        
        // 如果登录完成或失败，停止轮询
        const finalStatuses: LoginStatus[] = [LoginStatus.SUCCESS, LoginStatus.FAILED, LoginStatus.EXPIRED, LoginStatus.CANCELLED];
        if (finalStatuses.includes(updatedSession.status)) {
          clearInterval(pollInterval);
        }
      } catch {
        clearInterval(pollInterval);
      }
    }, 2000);

    // 10分钟后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 10 * 60 * 1000);
  };

  const confirmLogin = async () => {
    if (!session) return;
    
    try {
      setLoading(true);
      await douyinApi.confirmLogin(session.session_id);
      
      // 重新获取状态
      const updatedSession = await douyinApi.getLoginStatus(session.session_id);
      setSession(updatedSession);
      
      if (updatedSession.status === LoginStatus.SUCCESS) {
        try {
          const cookieData = await douyinApi.getCookies(session.session_id);

          const validationResult = await douyinApi.validateCookies({
            cookies: cookieData.cookies,
            timeout_seconds: 10
          });

          if (validationResult.is_valid) {
            try {
              await MultiAccountManager.addAccount(
                cookieData.cookies,
                session.session_id,
                validationResult
              );
            } catch (addError) {
              if (!(addError instanceof Error && addError.message.includes('已存在'))) {
                throw addError;
              }
            }

            localStorage.setItem('douyin-cookies', JSON.stringify(cookieData));
            localStorage.setItem('douyin-session-id', session.session_id);

            const loginStatus = {
              isLoggedIn: true,
              lastValidation: new Date().toISOString(),
              userInfo: validationResult
            };
            localStorage.setItem('douyin-login-status', JSON.stringify(loginStatus));
            localStorage.setItem('douyin-last-validation', loginStatus.lastValidation);
          }

          navigate('/dashboard');
        } catch {
          setError('获取登录凭证失败，请重试');
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '确认登录失败');
    } finally {
      setLoading(false);
    }
  };

  const cancelLogin = async () => {
    if (!session) return;
    
    try {
      await douyinApi.cancelLogin(session.session_id);
      setSession(null);
    } catch {
      // 忽略取消登录错误
    }
  };

  const getStatusInfo = () => {
    if (!session) return null;

    switch (session.status) {
      case LoginStatus.PENDING:
        return {
          icon: Clock,
          color: theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600',
          bgColor: theme === 'dark' ? 'bg-yellow-500/20' : 'bg-yellow-100',
          title: '正在启动浏览器...',
          description: '请稍候，浏览器正在打开中',
        };
      case LoginStatus.READY:
        return {
          icon: Video,
          color: theme === 'dark' ? 'text-blue-400' : 'text-blue-600',
          bgColor: theme === 'dark' ? 'bg-blue-500/20' : 'bg-blue-100',
          title: '请扫码登录',
          description: '请使用抖音APP扫描浏览器中的二维码',
        };
      case LoginStatus.SCANNING:
        return {
          icon: Clock,
          color: theme === 'dark' ? 'text-blue-400' : 'text-blue-600',
          bgColor: theme === 'dark' ? 'bg-blue-500/20' : 'bg-blue-100',
          title: '扫码中...',
          description: '检测到扫码，请在手机上确认登录',
        };
      case LoginStatus.SUCCESS:
        return {
          icon: CheckCircle,
          color: theme === 'dark' ? 'text-green-400' : 'text-green-600',
          bgColor: theme === 'dark' ? 'bg-green-500/20' : 'bg-green-100',
          title: '登录成功！',
          description: 'Cookie已成功获取，即将跳转到仪表板',
        };
      case LoginStatus.FAILED:
        return {
          icon: XCircle,
          color: theme === 'dark' ? 'text-red-400' : 'text-red-600',
          bgColor: theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100',
          title: '登录失败',
          description: session.error_message || '登录过程中出现错误',
        };
      case LoginStatus.EXPIRED:
        return {
          icon: XCircle,
          color: theme === 'dark' ? 'text-red-400' : 'text-red-600',
          bgColor: theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100',
          title: '会话已过期',
          description: '登录会话已超时，请重新开始',
        };
      case LoginStatus.CANCELLED:
        return {
          icon: XCircle,
          color: theme === 'dark' ? 'text-gray-400' : 'text-gray-600',
          bgColor: theme === 'dark' ? 'bg-gray-500/20' : 'bg-gray-100',
          title: '登录已取消',
          description: '用户取消了登录操作',
        };
      default:
        return null;
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <Layout>
      {/* 背景装饰 */}
      <div className={`fixed inset-0 ${
        theme === 'dark'
          ? 'bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900'
          : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
      }`}>
        <div className={`absolute inset-0 ${
          theme === 'dark'
            ? 'bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-red-900/20 via-transparent to-transparent'
            : 'bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-red-100/30 via-transparent to-transparent'
        }`} />
      </div>

      <div className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">

          {isCheckingLogin && (
            <>
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => navigate(isAddMode ? '/dashboard' : '/login')}
                  icon={ArrowLeft}
                  className={`${
                    theme === 'dark'
                      ? 'text-dark-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  } transition-colors duration-200`}
                >
                  返回
                </Button>
              </div>

              <div className="text-center space-y-6">
                <Loading size="lg" />

                <div>
                  <h2 className={`text-3xl font-bold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {loginCheckMessage.includes('跳转') ? '登录验证成功' : '检查登录状态'}
                  </h2>
                  <p className={`text-lg ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    {loginCheckMessage}
                  </p>
                  {loginCheckMessage.includes('跳转') && (
                    <div className="mt-4 flex items-center justify-center space-x-2">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span className="text-green-500 font-medium">即将跳转到仪表盘</span>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {!isCheckingLogin && (
            <>
              <div className="flex items-center">
                <Button
                  variant="ghost"
                  onClick={() => navigate(isAddMode ? '/dashboard' : '/login')}
                  icon={ArrowLeft}
                  className={`${
                    theme === 'dark'
                      ? 'text-dark-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  } transition-colors duration-200`}
                >
                  返回
                </Button>
              </div>

          {/* 标题区域 */}
          <div className="text-center space-y-4">
            <div className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl ${
              theme === 'dark'
                ? 'bg-gradient-to-r from-red-600 to-pink-500'
                : 'bg-gradient-to-r from-red-500 to-pink-600'
            } shadow-lg`}>
              <Radar className="h-8 w-8 text-white" />
            </div>

            <div>
              <h2 className={`text-3xl font-bold mb-2 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {isAddMode ? '添加抖音账号' : '抖音登录'}
              </h2>
              <p className={`text-lg ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {isAddMode ? '添加新的抖音账号到您的账号列表' : '使用抖音账号登录ReachRadar'}
              </p>
            </div>
          </div>

          {/* 登录状态卡片 */}
          <Card variant="premium" className="text-center">
            {!session ? (
              // 未开始登录
              <div className="space-y-6 p-2">
                <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${
                  theme === 'dark' ? 'bg-red-500/20' : 'bg-red-100'
                } shadow-lg`}>
                  <Video className={`h-10 w-10 ${
                    theme === 'dark' ? 'text-red-400' : 'text-red-600'
                  }`} />
                </div>
                <div>
                  <h3 className={`text-xl font-semibold mb-2 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    {isAddMode ? '准备添加账号' : '准备登录抖音'}
                  </h3>
                  <p className={`mb-6 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    点击下方按钮开始{isAddMode ? '添加账号' : '登录'}流程，我们将打开浏览器窗口
                  </p>
                  <Button
                    onClick={startLogin}
                    loading={loading}
                    disabled={loading}
                    className="w-full"
                  >
                    {isAddMode ? '开始添加账号' : '开始登录'}
                  </Button>
                </div>
              </div>
            ) : (
              // 登录进行中
              <div className="space-y-6 p-2">
                {statusInfo && (
                  <>
                    <div className={`flex items-center justify-center w-20 h-20 rounded-2xl mx-auto ${statusInfo.bgColor} shadow-lg`}>
                      <statusInfo.icon className={`h-10 w-10 ${statusInfo.color}`} />
                    </div>
                    <div>
                      <h3 className={`text-xl font-semibold mb-2 ${
                        theme === 'dark' ? 'text-white' : 'text-gray-900'
                      }`}>
                        {statusInfo.title}
                      </h3>
                      <p className={`mb-6 ${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`}>
                        {statusInfo.description}
                      </p>
                    </div>
                  </>
                )}

                {/* 操作按钮 */}
                <div className="space-y-3">
                  {session.status === LoginStatus.READY && (
                    <Button
                      onClick={confirmLogin}
                      loading={loading}
                      disabled={loading}
                      className="w-full"
                    >
                      我已完成扫码
                    </Button>
                  )}
                  
                  {session.status === LoginStatus.SUCCESS && (
                    <Button
                      onClick={() => navigate('/dashboard')}
                      className="w-full"
                    >
                      进入仪表板
                    </Button>
                  )}
                  
                  {(session.status === LoginStatus.FAILED || session.status === LoginStatus.EXPIRED) && (
                    <Button
                      onClick={startLogin}
                      loading={loading}
                      disabled={loading}
                      className="w-full"
                    >
                      重新登录
                    </Button>
                  )}
                  
                  {(session.status === LoginStatus.PENDING || session.status === LoginStatus.READY || session.status === LoginStatus.SCANNING) && (
                    <Button
                      variant="outline"
                      onClick={cancelLogin}
                      className="w-full"
                    >
                      取消登录
                    </Button>
                  )}
                </div>
              </div>
            )}
          </Card>

          {/* 错误提示 */}
          {error && (
            <Card
              variant="premium"
              className={`${
                theme === 'dark'
                  ? 'border-red-500/50 bg-red-500/10'
                  : 'border-red-300/50 bg-red-50'
              }`}
            >
              <div className="flex items-center space-x-3">
                <XCircle className={`h-5 w-5 flex-shrink-0 ${
                  theme === 'dark' ? 'text-red-400' : 'text-red-600'
                }`} />
                <p className={`text-sm ${
                  theme === 'dark' ? 'text-red-300' : 'text-red-700'
                }`}>
                  {error}
                </p>
              </div>
            </Card>
          )}

          {/* 说明文字 */}
          <div className="text-center">
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full ${
              theme === 'dark'
                ? 'bg-dark-800/50 border border-dark-700'
                : 'bg-white/80 border border-gray-200'
            } backdrop-blur-sm`}>
              <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" />
              <p className={`text-sm ${
                theme === 'dark' ? 'text-dark-400' : 'text-gray-600'
              }`}>
                登录过程中请保持浏览器窗口打开
              </p>
            </div>
          </div>
          </>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default DouyinLoginPage;
