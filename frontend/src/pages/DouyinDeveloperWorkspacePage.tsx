import React, { useState, useEffect, useCallback } from 'react';
import { ArrowLeft, Crown, Users } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import Card from '../components/Card';
import Button from '../components/Button';
import UserSelector from '../components/UserSelector';
import TopicSearchComponent from '../components/TopicSearchComponent';
import CommentAnalysisComponent from '../components/CommentAnalysisComponent';
import CustomerAnalysisComponent from '../components/CustomerAnalysisComponent';
import UserValidationComponent from '../components/UserValidationComponent';
import CookieExportComponent from '../components/CookieExportComponent';
import { NetworkSecurityManager } from '../utils/networkSecurity';
import type { DouyinAccount } from '../utils/multiAccount';
import type { SearchResponse, CommentResult } from '../types';

interface UserTaskState {
  account: DouyinAccount;
  searchResults: SearchResponse | null;
  searchKeyword: string;
  commentResult: CommentResult | null;
  isProcessing: boolean;
  currentStep: 'search' | 'comment' | 'analysis' | 'completed';
  error: string | null;
}

const DouyinDeveloperWorkspacePage: React.FC = () => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [selectedUsers, setSelectedUsers] = useState<DouyinAccount[]>([]);
  const [userTasks, setUserTasks] = useState<Map<string, UserTaskState>>(new Map());

  useEffect(() => {
    checkNetworkSecurity();
  }, []);

  const checkNetworkSecurity = async () => {
    try {
      await NetworkSecurityManager.checkNetworkSecurity();
    } catch {
      // 静默处理错误
    }
  };

  const handleUserChange = useCallback((users: DouyinAccount[]) => {
    setSelectedUsers(users);

    // 使用函数式更新来避免依赖userTasks
    setUserTasks(prevUserTasks => {
      const newUserTasks = new Map(prevUserTasks);

      // 移除不再选中的用户任务
      for (const [userId] of newUserTasks) {
        if (!users.some(user => user.id === userId)) {
          newUserTasks.delete(userId);
        }
      }

      // 为新选中的用户初始化任务状态
      users.forEach(user => {
        if (!newUserTasks.has(user.id)) {
          newUserTasks.set(user.id, {
            account: user,
            searchResults: null,
            searchKeyword: '',
            commentResult: null,
            isProcessing: false,
            currentStep: 'search',
            error: null
          });
        }
      });

      return newUserTasks;
    });
  }, []);

  const handleSearchComplete = useCallback((userId: string, results: SearchResponse, keyword: string) => {
    setUserTasks(prev => {
      const newTasks = new Map(prev);
      const task = newTasks.get(userId);
      if (task) {
        newTasks.set(userId, {
          ...task,
          searchResults: results,
          searchKeyword: keyword,
          currentStep: 'comment',
          error: null
        });
      }
      return newTasks;
    });
  }, []);

  const handleCommentAnalysisComplete = useCallback((userId: string, result: CommentResult) => {
    setUserTasks(prev => {
      const newTasks = new Map(prev);
      const task = newTasks.get(userId);
      if (task) {
        newTasks.set(userId, {
          ...task,
          commentResult: result,
          currentStep: 'analysis',
          error: null
        });
      }
      return newTasks;
    });
  }, []);





  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleReLogin = () => {
    navigate('/login/douyin');
  };



  return (
    <Layout>
      <div className="min-h-screen py-8">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <Button
                onClick={handleBackToDashboard}
                className="bg-gray-500 hover:bg-gray-600 flex items-center space-x-2"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回仪表板</span>
              </Button>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-12 h-12 bg-yellow-500/20 rounded-xl">
                  <Crown className="h-6 w-6 text-yellow-400" />
                </div>
                <div>
                  <h1 className={`text-2xl font-bold transition-colors duration-300 ${
                    theme === 'dark' ? 'text-white' : 'text-gray-900'
                  }`}>
                    开发者工作台
                  </h1>
                  <p className={`transition-colors duration-300 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    多用户批量操作和分析
                  </p>
                </div>
              </div>
            </div>


          </div>

          <CookieExportComponent />

          <UserSelector
            isDeveloper={true}
            multiSelectMode={true}
            onUserChange={handleUserChange}
          />



          {selectedUsers.length === 0 && (
            <Card variant="glass" padding="lg">
              <div className="text-center py-12">
                <Users className={`h-16 w-16 mx-auto mb-4 ${
                  theme === 'dark' ? 'text-dark-400' : 'text-gray-400'
                }`} />
                <h3 className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  请选择用户账号
                </h3>
                <p className={`transition-colors duration-300 ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  选择用户账号开始操作
                </p>
              </div>
            </Card>
          )}

          {/* 为每个选中的用户显示独立的工作区 */}
          {selectedUsers.map((user) => {
            const task = userTasks.get(user.id);
            if (!task) return null;

            return (
              <div key={user.id} className="mb-8">
                <Card variant="glass" padding="lg" className="mb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      {user.avatar ? (
                        <img
                          src={user.avatar}
                          alt={user.nickname}
                          className="w-10 h-10 rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <Users className="h-5 w-5 text-gray-600" />
                        </div>
                      )}
                      <div>
                        <h4 className={`font-semibold transition-colors duration-300 ${
                          theme === 'dark' ? 'text-white' : 'text-gray-900'
                        }`}>
                          {user.nickname}
                        </h4>
                        <p className={`text-sm transition-colors duration-300 ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          当前步骤: {task.currentStep === 'search' ? '话题搜索' :
                                   task.currentStep === 'comment' ? '获取评论' :
                                   task.currentStep === 'analysis' ? '客户分析' : '已完成'}
                        </p>
                      </div>
                    </div>
                    

                  </div>
                </Card>

                <UserValidationComponent
                  currentAccount={user}
                  onValidationComplete={() => {}}
                  onReLogin={handleReLogin}
                />

                <TopicSearchComponent
                  currentAccount={user}
                  onSearchComplete={(results, keyword) => handleSearchComplete(user.id, results, keyword)}
                />

                {task.searchResults && (
                  <CommentAnalysisComponent
                    currentAccount={user}
                    onAnalysisComplete={(result) => handleCommentAnalysisComplete(user.id, result)}
                  />
                )}

                {task.commentResult && (
                  <CustomerAnalysisComponent
                    commentResult={task.commentResult}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </Layout>
  );
};

export default DouyinDeveloperWorkspacePage;
