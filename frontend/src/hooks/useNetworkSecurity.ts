import { useState, useCallback } from 'react';
import { NetworkSecurityManager } from '../utils/networkSecurity';
import type { NetworkSecurity } from '../types';

export const useNetworkSecurity = () => {
  const [networkSecurity, setNetworkSecurity] = useState<NetworkSecurity | null>(null);
  const [showNetworkWarning, setShowNetworkWarning] = useState(false);
  const [isChecking, setIsChecking] = useState(false);

  const checkNetworkSecurity = useCallback(async (): Promise<boolean> => {
    try {
      setIsChecking(true);
      const security = await NetworkSecurityManager.checkNetworkSecurity();
      setNetworkSecurity(security);
      
      if (!security.is_safe) {
        setShowNetworkWarning(true);
        return false;
      }
      
      setShowNetworkWarning(false);
      return true;
    } catch (error) {
      console.warn('网络安全检查失败:', error);
      setShowNetworkWarning(false);
      return true;
    } finally {
      setIsChecking(false);
    }
  }, []);

  const getNetworkErrorMessage = useCallback((): string => {
    if (!networkSecurity || networkSecurity.is_safe) {
      return '';
    }
    
    return '当前网络受限制，请切换网络后重试';
  }, [networkSecurity]);

  const resetNetworkWarning = useCallback(() => {
    setShowNetworkWarning(false);
  }, []);

  const setNetworkSecurityManually = useCallback((security: NetworkSecurity) => {
    setNetworkSecurity(security);
    setShowNetworkWarning(!security.is_safe);
  }, []);

  return {
    networkSecurity,
    showNetworkWarning,
    isChecking,
    checkNetworkSecurity,
    getNetworkErrorMessage,
    resetNetworkWarning,
    setNetworkSecurityManually
  };
};

export const useSimpleNetworkCheck = () => {
  const checkNetworkSecurity = useCallback(async (): Promise<boolean> => {
    try {
      const security = await NetworkSecurityManager.checkNetworkSecurity();
      return security.is_safe;
    } catch (error) {
      console.warn('网络安全检查失败:', error);
      return true;
    }
  }, []);

  return { checkNetworkSecurity };
};
