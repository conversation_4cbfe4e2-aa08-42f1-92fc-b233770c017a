import React, { useState, useEffect } from 'react';
import { CheckCircle, User, Loader2, AlertTriangle } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { douyinApi } from '../services/api';
import { useNetworkSecurity } from '../hooks/useNetworkSecurity';
import type { CookieValidationResponse } from '../types';
import type { DouyinAccount } from '../utils/multiAccount';

const ValidationStatus = {
  VALIDATING: 'validating',
  VALID: 'valid',
  INVALID: 'invalid'
} as const;

type ValidationStatusType = typeof ValidationStatus[keyof typeof ValidationStatus];

interface UserValidationProps {
  currentAccount: DouyinAccount;
  onValidationComplete: (userInfo: CookieValidationResponse | null, isValid: boolean) => void;
  onReLogin: () => void;
}

const UserValidationComponent: React.FC<UserValidationProps> = ({
  currentAccount,
  onValidationComplete,
  onReLogin
}) => {
  const { theme } = useTheme();
  const {
    networkSecurity,
    showNetworkWarning,
    checkNetworkSecurity,
    resetNetworkWarning
  } = useNetworkSecurity();
  const [validationStatus, setValidationStatus] = useState<ValidationStatusType>(ValidationStatus.VALIDATING);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [userInfo, setUserInfo] = useState<CookieValidationResponse | null>(null);



  const validateCookies = async () => {
    try {
      setValidationStatus(ValidationStatus.VALIDATING);
      setValidationError(null);

      const isNetworkSafe = await checkNetworkSecurity();
      if (!isNetworkSafe) {
        setValidationStatus(ValidationStatus.INVALID);
        setValidationError('当前网络受限制，请切换网络后重试');
        onValidationComplete(null, false);
        return;
      }

      if (!currentAccount) {
        setValidationStatus(ValidationStatus.INVALID);
        setValidationError('未找到账号信息，请重新登录');
        onValidationComplete(null, false);
        return;
      }

      const result = await douyinApi.validateCookies({
        cookies: currentAccount.cookies,
        timeout_seconds: 10
      });

      setUserInfo(result);
      setValidationStatus(ValidationStatus.VALID);
      onValidationComplete(result, true);

    } catch (error) {
      setValidationStatus(ValidationStatus.INVALID);
      let errorMessage = '验证失败，请重新登录';
      
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      setValidationError(errorMessage);
      onValidationComplete(null, false);
    }
  };

  useEffect(() => {
    if (currentAccount) {
      validateCookies();
    }
  }, [currentAccount]);

  const dismissNetworkWarning = () => {
    resetNetworkWarning();
  };

  if (validationStatus === ValidationStatus.VALIDATING) {
    return (
      <Card variant="glass" padding="lg" className="mb-8">
        <div className="text-center">
          <Loader2 className={`h-8 w-8 animate-spin mx-auto mb-4 ${
            theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
          }`} />
          <h2 className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>验证登录状态</h2>
          <p className={`transition-colors duration-300 ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            正在验证您的登录凭证...
          </p>
        </div>
      </Card>
    );
  }

  if (validationStatus === ValidationStatus.INVALID) {
    return (
      <Card variant="glass" padding="lg" className="mb-8">
        <div className="text-center">
          <h2 className={`text-xl font-semibold mb-2 transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>登录验证失败</h2>
          <p className={`mb-4 transition-colors duration-300 ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            {validationError}
          </p>
          <Button onClick={onReLogin} className="bg-red-500 hover:bg-red-600">
            重新登录
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <>
      {showNetworkWarning && networkSecurity && (
        <Card variant="glass" padding="lg" className="mb-4">
          <div className={`p-4 rounded-lg ${
            theme === 'dark' ? 'bg-yellow-900/20 border border-yellow-700/30' : 'bg-yellow-50 border border-yellow-200'
          }`}>
            <div className="flex items-start space-x-3">
              <AlertTriangle className={`h-5 w-5 mt-0.5 ${
                theme === 'dark' ? 'text-yellow-400' : 'text-yellow-600'
              }`} />
              <div className="flex-1">
                <h4 className={`font-medium ${
                  theme === 'dark' ? 'text-yellow-400' : 'text-yellow-800'
                }`}>
                  网络安全警告
                </h4>
                <p className={`text-sm mt-1 ${
                  theme === 'dark' ? 'text-yellow-300' : 'text-yellow-700'
                }`}>
                  {networkSecurity.message}
                </p>
                <div className="mt-3 flex space-x-2">
                  <Button
                    onClick={validateCookies}
                    className="bg-yellow-500 hover:bg-yellow-600 text-white text-sm px-3 py-1"
                  >
                    重新检测
                  </Button>
                  <Button
                    onClick={dismissNetworkWarning}
                    className="bg-gray-500 hover:bg-gray-600 text-white text-sm px-3 py-1"
                  >
                    忽略警告
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      <Card variant="glass" padding="lg" className="mb-8">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-12 h-12 bg-green-500/20 rounded-full">
            <CheckCircle className="h-6 w-6 text-green-400" />
          </div>
          <div className="flex-1">
            <h3 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              登录验证成功
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <User className={`h-4 w-4 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`} />
              <span className={`transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {userInfo?.user_data?.user_info?.nickname || currentAccount.nickname}
              </span>
            </div>
          </div>
          <Button
            onClick={validateCookies}
            className="bg-blue-500 hover:bg-blue-600 text-sm px-3 py-1"
          >
            重新验证
          </Button>
        </div>
      </Card>
    </>
  );
};

export default UserValidationComponent;
