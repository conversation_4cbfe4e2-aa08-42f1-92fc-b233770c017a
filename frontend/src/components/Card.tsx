import React from 'react';
import { useTheme } from '../hooks/useTheme';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'gradient' | 'premium';
  padding?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  onClick?: () => void;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  variant = 'default',
  padding = 'md',
  hover = false,
  onClick,
}) => {
  const { theme } = useTheme();
  const baseClasses = 'rounded-2xl border transition-all duration-300 ease-out relative overflow-hidden';

  const getVariantClasses = () => {
    switch (variant) {
      case 'default':
        return theme === 'dark'
          ? 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-700/50 shadow-xl shadow-slate-900/20 backdrop-blur-sm'
          : 'bg-gradient-to-br from-white to-gray-50/80 border-gray-200/60 shadow-xl shadow-gray-900/10 backdrop-blur-sm';

      case 'glass':
        return theme === 'dark'
          ? 'bg-white/5 backdrop-blur-xl border-white/10 shadow-2xl shadow-slate-900/30'
          : 'bg-white/70 backdrop-blur-xl border-white/40 shadow-2xl shadow-gray-900/15';

      case 'gradient':
        return theme === 'dark'
          ? 'bg-gradient-to-br from-slate-800 via-slate-900 to-slate-800 border-slate-600/50 shadow-2xl shadow-slate-900/40'
          : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-blue-200/60 shadow-2xl shadow-blue-900/15';

      case 'premium':
        return theme === 'dark'
          ? 'bg-gradient-to-br from-slate-800/95 to-slate-900/95 border-gradient-dark shadow-2xl shadow-slate-900/50'
          : 'bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 border-gradient-light shadow-2xl shadow-indigo-900/20';

      default:
        return theme === 'dark'
          ? 'bg-gradient-to-br from-slate-800/90 to-slate-900/90 border-slate-700/50 shadow-xl shadow-slate-900/20'
          : 'bg-gradient-to-br from-white to-gray-50/80 border-gray-200/60 shadow-xl shadow-gray-900/10';
    }
  };

  const paddingClasses = {
    sm: 'p-5',
    md: 'p-7',
    lg: 'p-9',
  };

  const hoverClasses = hover
    ? 'hover:shadow-2xl hover:-translate-y-1 hover:scale-[1.02] cursor-pointer card-hover-effect'
    : '';

  const classes = [
    baseClasses,
    getVariantClasses(),
    paddingClasses[padding],
    hoverClasses,
    className,
  ].join(' ');

  return (
    <div className={classes} onClick={onClick}>
      {/* 微妙的光泽效果 */}
      {hover && (
        <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300">
          <div className={`absolute inset-0 rounded-2xl ${
            theme === 'dark'
              ? 'bg-gradient-to-r from-transparent via-white/5 to-transparent'
              : 'bg-gradient-to-r from-transparent via-blue-100/30 to-transparent'
          } transform -skew-x-12 -translate-x-full hover:translate-x-full transition-transform duration-1000 ease-out`} />
        </div>
      )}
      {children}
    </div>
  );
};

export default Card;
