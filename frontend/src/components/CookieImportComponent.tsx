import React, { useState } from 'react';
import { Upload, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Button from './Button';
import Card from './Card';
import { MultiAccountManager } from '../utils/multiAccount';
import type { CookieData } from '../types';

interface ImportedAccountData {
  accountId: string;
  nickname: string;
  uid: string;
  avatar?: string;
  sessionId: string;
  addedAt: string;
  lastValidation?: string;
  isValid?: boolean;
  cookies: CookieData[];
  userInfo?: any;
}

interface ImportData {
  exportDate: string;
  totalAccounts: number;
  accounts: ImportedAccountData[];
}

interface CookieImportComponentProps {
  isDeveloper: boolean;
}

const CookieImportComponent: React.FC<CookieImportComponentProps> = ({ isDeveloper }) => {
  const { theme } = useTheme();
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [importResult, setImportResult] = useState<{
    success: number;
    failed: number;
    errors: string[];
  } | null>(null);
  const [inputValue, setInputValue] = useState('');

  if (!isDeveloper) {
    return null;
  }

  const validateImportData = (data: any): data is ImportData => {
    if (!data || typeof data !== 'object') return false;
    if (!Array.isArray(data.accounts)) return false;
    if (typeof data.totalAccounts !== 'number') return false;
    
    return data.accounts.every((account: any) => 
      typeof account.accountId === 'string' &&
      typeof account.nickname === 'string' &&
      typeof account.uid === 'string' &&
      typeof account.sessionId === 'string' &&
      Array.isArray(account.cookies) &&
      account.cookies.every((cookie: any) => 
        typeof cookie.name === 'string' &&
        typeof cookie.value === 'string' &&
        typeof cookie.domain === 'string'
      )
    );
  };

  const processImport = async (data: ImportData) => {
    const results = { success: 0, failed: 0, errors: [] as string[] };
    
    for (const accountData of data.accounts) {
      try {
        // 检查账号是否已存在
        const existingAccounts = MultiAccountManager.getAccounts();
        const existingAccount = existingAccounts.find(acc => acc.uid === accountData.uid);
        
        if (existingAccount) {
          results.errors.push(`账号 ${accountData.nickname} (UID: ${accountData.uid}) 已存在，跳过导入`);
          results.failed++;
          continue;
        }

        // 创建用户信息对象
        const userInfo = accountData.userInfo || {
          user_data: {
            user_info: {
              uid: accountData.uid,
              nickname: accountData.nickname,
              avatar_thumb: accountData.avatar
            }
          }
        };

        // 添加账号
        await MultiAccountManager.addAccount(
          accountData.cookies,
          accountData.sessionId,
          {
            is_valid: true,
            user_data: userInfo.user_data,
            validation_time: new Date().toISOString()
          }
        );

        results.success++;
      } catch (error) {
        results.errors.push(`导入账号 ${accountData.nickname} 失败: ${error instanceof Error ? error.message : '未知错误'}`);
        results.failed++;
      }
    }

    return results;
  };


  const handleImport = async () => {
    const text = inputValue.trim();
    if (!text) {
      setImportStatus('error');
      setImportResult({
        success: 0,
        failed: 1,
        errors: ['请输入有效的JSON数据']
      });
      return;
    }

    setImportStatus('processing');
    
    try {
      const data = JSON.parse(text);
      
      if (!validateImportData(data)) {
        throw new Error('数据格式不正确，请确保是从导出功能生成的有效JSON数据');
      }

      const results = await processImport(data);
      setImportResult(results);
      setImportStatus(results.success > 0 ? 'success' : 'error');
      
      if (results.success > 0) {
        setInputValue('');
      }
    } catch (error) {
      setImportStatus('error');
      setImportResult({
        success: 0,
        failed: 1,
        errors: [error instanceof Error ? error.message : '导入失败']
      });
    }
  };


  const resetImport = () => {
    setImportStatus('idle');
    setImportResult(null);
    setInputValue('');
  };

  return (
    <Card variant="glass" padding="md" className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-green-500/20 rounded-lg">
            <Upload className="h-5 w-5 text-green-400" />
          </div>
          <div>
            <h4 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              账号导入
            </h4>
            <p className={`text-sm transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              批量导入账号cookie数据
            </p>
          </div>
        </div>
      </div>

      {importStatus === 'idle' && (
        <div className="space-y-4">
          <div className="relative">
            <textarea
              placeholder="在此处粘贴从开发者工作台导出的JSON数据..."
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              className={`w-full h-32 px-4 py-3 rounded-lg border resize-none transition-colors duration-300 ${
                theme === 'dark'
                  ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
            />
          </div>
          <div className="flex justify-end">
            <Button
              onClick={handleImport}
              className="bg-blue-500 hover:bg-blue-600"
              disabled={!inputValue.trim()}
            >
              导入数据
            </Button>
          </div>
        </div>
      )}

      {importStatus === 'processing' && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className={`transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            正在导入账号数据...
          </p>
        </div>
      )}

      {(importStatus === 'success' || importStatus === 'error') && importResult && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {importStatus === 'success' ? (
                <CheckCircle className="h-6 w-6 text-green-500" />
              ) : (
                <XCircle className="h-6 w-6 text-red-500" />
              )}
              <div>
                <h5 className={`font-medium transition-colors duration-300 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  导入完成
                </h5>
                <p className={`text-sm transition-colors duration-300 ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  成功: {importResult.success} 个，失败: {importResult.failed} 个
                </p>
              </div>
            </div>
            <Button
              onClick={resetImport}
              variant="outline"
              size="sm"
            >
              重新导入
            </Button>
          </div>

          {importResult.errors.length > 0 && (
            <div className={`p-3 rounded-lg border-l-4 border-yellow-500 ${
              theme === 'dark' 
                ? 'bg-yellow-500/10 border-yellow-400' 
                : 'bg-yellow-50 border-yellow-500'
            }`}>
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-5 w-5 text-yellow-500 mt-0.5" />
                <div className="flex-1">
                  <p className={`font-medium text-sm transition-colors duration-300 ${
                    theme === 'dark' ? 'text-yellow-300' : 'text-yellow-700'
                  }`}>
                    导入详情:
                  </p>
                  <ul className={`text-sm mt-1 space-y-1 transition-colors duration-300 ${
                    theme === 'dark' ? 'text-yellow-200' : 'text-yellow-600'
                  }`}>
                    {importResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* 使用说明 */}
      <div className={`mt-4 p-3 rounded-lg border-l-4 border-blue-500 ${
        theme === 'dark' 
          ? 'bg-blue-500/10 border-blue-400' 
          : 'bg-blue-50 border-blue-500'
      }`}>
        <p className={`text-sm transition-colors duration-300 ${
          theme === 'dark' ? 'text-blue-300' : 'text-blue-700'
        }`}>
          <strong>使用说明：</strong>
          粘贴从开发者工作台"账号迁移"功能导出的JSON数据，自动批量添加账号到当前设备。
        </p>
      </div>
    </Card>
  );
};

export default CookieImportComponent;