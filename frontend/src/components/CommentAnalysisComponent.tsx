import React, { useState, useEffect } from 'react';
import { MessageSquare, Loader2, Download, Heart, MessageCircle, Calendar, MapPin, Video, User } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { douyinApi } from '../services/api';
import { formatTime, formatNumber } from '../utils/formatters';
import { useSimpleNetworkCheck } from '../hooks/useNetworkSecurity';
import type { CommentResult, VideoFilterInfo, RealEstateAnalysisResponse } from '../types';
import type { DouyinAccount } from '../utils/multiAccount';

const CommentStatus = {
  IDLE: 'idle',
  FETCHING: 'fetching',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

type CommentStatusType = typeof CommentStatus[keyof typeof CommentStatus];

interface CommentConfig {
  maxMonthsOld: number;
  minCommentCount: number;
}

interface CommentAnalysisProps {
  currentAccount: DouyinAccount;
  realEstateAnalysisResult?: RealEstateAnalysisResponse | null;
  onAnalysisComplete: (result: CommentResult) => void;
  showCommentDetails?: boolean;
}

const CommentAnalysisComponent: React.FC<CommentAnalysisProps> = ({
  currentAccount,
  realEstateAnalysisResult,
  onAnalysisComplete,
  showCommentDetails = true
}) => {
  const { theme } = useTheme();
  const { checkNetworkSecurity } = useSimpleNetworkCheck();
  const [commentStatus, setCommentStatus] = useState<CommentStatusType>(CommentStatus.IDLE);
  const [commentError, setCommentError] = useState<string | null>(null);
  const [commentResult, setCommentResult] = useState<CommentResult | null>(null);
  const [videoList, setVideoList] = useState<VideoFilterInfo[]>([]);
  const [loadingVideos, setLoadingVideos] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState({ current: 0, total: 0 });
  const [commentConfig, setCommentConfig] = useState<CommentConfig>({
    maxMonthsOld: 6,
    minCommentCount: 10
  });

  // 当房地产分析结果变化时加载视频数据
  useEffect(() => {
    if (realEstateAnalysisResult) {
      loadVideosFromAnalysisResult();
    }
  }, [realEstateAnalysisResult]);

  const loadVideosFromAnalysisResult = async () => {
    try {
      setLoadingVideos(true);
      setCommentError(null);
      setLoadingProgress({ current: 0, total: 0 });

      if (!realEstateAnalysisResult?.results) {
        setCommentError('没有房地产分析结果数据');
        return;
      }

      // 筛选出房地产相关的视频
      const relatedVideos = realEstateAnalysisResult.results.filter(
        result => result.is_real_estate_related
      );

      if (relatedVideos.length === 0) {
        setCommentError('分析结果中没有找到房地产相关的视频');
        return;
      }

      setLoadingProgress({ current: 0, total: relatedVideos.length });

      // 转换为VideoFilterInfo格式
      const allVideoList: VideoFilterInfo[] = relatedVideos.map((result) => ({
        aweme_id: result.aweme_id,
        comment_count: result.comment_count || 0,
        create_time: result.create_time || 0
      }));

      setVideoList(allVideoList);
      setLoadingProgress({ current: allVideoList.length, total: allVideoList.length });
    } catch (error) {
      let errorMessage = '加载视频数据失败，请稍后重试';
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      setCommentError(errorMessage);
    } finally {
      setLoadingVideos(false);
      setLoadingProgress({ current: 0, total: 0 });
    }
  };

  const handleFetchComments = async () => {
    // 如果还没有加载视频数据，先加载
    if (videoList.length === 0) {
      if (realEstateAnalysisResult) {
        await loadVideosFromAnalysisResult();
      } else {
        setCommentError('请先进行房地产分析');
      }
      return;
    }

    const isNetworkSafe = await checkNetworkSecurity();
    if (!isNetworkSafe) {
      setCommentError('当前网络受限制，请切换网络后重试');
      return;
    }

    try {
      setCommentStatus(CommentStatus.FETCHING);
      setCommentError(null);
      setCommentResult(null);

      const requestData = {
        video_list: videoList,
        cookies: currentAccount.cookies,
        max_months_old: commentConfig.maxMonthsOld,
        min_comment_count: commentConfig.minCommentCount,
        timeout_seconds: 600
      };

      const result = await douyinApi.getComments(requestData);

      setCommentResult(result);
      setCommentStatus(CommentStatus.SUCCESS);
      onAnalysisComplete(result);

    } catch (error) {
      setCommentStatus(CommentStatus.ERROR);
      let errorMessage = '获取评论失败，请稍后重试';

      if (error instanceof Error) {
        errorMessage = error.message;
      }

      setCommentError(errorMessage);
    }
  };



  const downloadReports = () => {
    if (!commentResult) {
      return;
    }

    const timestamp = new Date().getTime();

    if (commentResult.comments.length > 0) {
      const commentsCsvContent = [
        ['视频ID', '视频链接', '评论ID', '用户昵称', '抖音号', 'SecUID', '评论内容', '点赞数', '评论时间', 'IP地址'].join(','),
        ...commentResult.comments.map(comment => [
          comment.aweme_id,
          `"https://www.douyin.com/video/${comment.aweme_id}"`,
          comment.cid,
          `"${comment.user_nickname}"`,
          comment.user_short_id,
          comment.user_sec_uid,
          `"${comment.text.replace(/"/g, '""')}"`,
          comment.digg_count,
          formatTime(comment.create_time),
          comment.ip_label
        ].join(','))
      ].join('\n');

      const commentsBlob = new Blob([commentsCsvContent], { type: 'text/csv;charset=utf-8;' });
      const commentsLink = document.createElement('a');
      const commentsUrl = URL.createObjectURL(commentsBlob);
      commentsLink.setAttribute('href', commentsUrl);
      commentsLink.setAttribute('download', `douyin_comments_${timestamp}.csv`);
      commentsLink.style.visibility = 'hidden';
      document.body.appendChild(commentsLink);
      commentsLink.click();
      document.body.removeChild(commentsLink);
    }

    if (commentResult.skipped_videos && commentResult.skipped_videos.length > 0) {
      const skippedVideosCsvContent = [
        ['视频ID', '跳过原因', '评论数量', '发布时间', '视频链接'].join(','),
        ...commentResult.skipped_videos.map(video => [
          video.aweme_id,
          `"${video.reason.replace(/"/g, '""')}"`,
          video.comment_count || 0,
          video.create_time ? formatTime(video.create_time, { fallback: '-' }) : '-',
          `"https://www.douyin.com/video/${video.aweme_id}"`
        ].join(','))
      ].join('\n');

      const skippedBlob = new Blob([skippedVideosCsvContent], { type: 'text/csv;charset=utf-8;' });
      const skippedLink = document.createElement('a');
      const skippedUrl = URL.createObjectURL(skippedBlob);
      skippedLink.setAttribute('href', skippedUrl);
      skippedLink.setAttribute('download', `douyin_skipped_videos_${timestamp}.csv`);
      skippedLink.style.visibility = 'hidden';
      document.body.appendChild(skippedLink);
      skippedLink.click();
      document.body.removeChild(skippedLink);
    }
  };

  return (
    <Card variant="glass" padding="lg" className="mb-8">
      <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
        theme === 'dark' ? 'text-white' : 'text-gray-900'
      }`}>
        获取评论
      </h3>

      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-700'
            }`}>
              视频时间范围（月）
            </label>
            <input
              type="number"
              min="1"
              max="12"
              value={commentConfig.maxMonthsOld}
              onChange={(e) => setCommentConfig(prev => ({
                ...prev,
                maxMonthsOld: parseInt(e.target.value) || 6
              }))}
              className={`w-full px-3 py-2 rounded-lg border transition-all duration-300 ${
                theme === 'dark'
                  ? 'bg-dark-800 border-dark-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
            />
          </div>
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-700'
            }`}>
              最小评论数
            </label>
            <input
              type="number"
              min="1"
              value={commentConfig.minCommentCount}
              onChange={(e) => setCommentConfig(prev => ({
                ...prev,
                minCommentCount: parseInt(e.target.value) || 10
              }))}
              className={`w-full px-3 py-2 rounded-lg border transition-all duration-300 ${
                theme === 'dark'
                  ? 'bg-dark-800 border-dark-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
            />
          </div>
        </div>

        <div className="flex space-x-3">
          <Button
            onClick={handleFetchComments}
            disabled={commentStatus === CommentStatus.FETCHING || loadingVideos}
            className="bg-green-500 hover:bg-green-600 flex items-center space-x-2"
          >
            {(commentStatus === CommentStatus.FETCHING || loadingVideos) ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <MessageSquare className="h-4 w-4" />
            )}
            <span>
              {loadingVideos
                ? loadingProgress.total > 0
                  ? `加载视频数据中... (${loadingProgress.current}/${loadingProgress.total})`
                  : '加载视频数据中...'
                : commentStatus === CommentStatus.FETCHING
                ? '获取评论中...'
                : videoList.length > 0
                ? `获取评论 (${videoList.length} 个视频)`
                : '等待房地产分析结果'
              }
            </span>
          </Button>

          {commentResult && (commentResult.comments.length > 0 || commentResult.skipped_videos?.length > 0) && (
            <Button
              onClick={downloadReports}
              className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>下载报告</span>
            </Button>
          )}
        </div>

        {loadingVideos && loadingProgress.total > 0 && (
          <div className={`p-4 rounded-lg ${
            theme === 'dark' ? 'bg-dark-800' : 'bg-gray-50'
          }`}>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className={theme === 'dark' ? 'text-dark-300' : 'text-gray-600'}>
                  正在加载视频数据...
                </span>
                <span className={theme === 'dark' ? 'text-dark-300' : 'text-gray-600'}>
                  {loadingProgress.current} / {loadingProgress.total}
                </span>
              </div>
              <div className={`w-full bg-gray-200 rounded-full h-2 ${
                theme === 'dark' ? 'bg-dark-700' : 'bg-gray-200'
              }`}>
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{
                    width: `${Math.round((loadingProgress.current / loadingProgress.total) * 100)}%`
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {commentError && (
          <div className={`p-3 rounded-lg ${
            theme === 'dark' ? 'bg-red-900/20 text-red-400' : 'bg-red-50 text-red-600'
          }`}>
            {commentError}
          </div>
        )}

        {commentResult && (
          <div className={`p-4 rounded-lg ${
            theme === 'dark' ? 'bg-dark-800' : 'bg-gray-50'
          }`}>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
                }`}>
                  {commentResult.filter_stats?.total_videos || 0}
                </div>
                <div className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  获取总数
                </div>
              </div>
              <div>
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-green-400' : 'text-green-600'
                }`}>
                  {commentResult.comments.length}
                </div>
                <div className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  获取评论
                </div>
              </div>
              <div>
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-purple-400' : 'text-purple-600'
                }`}>
                  {Math.round((commentResult.filter_stats?.processed || 0) / (commentResult.filter_stats?.total_videos || 1) * 100)}%
                </div>
                <div className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  完成率
                </div>
              </div>
              <div>
                <div className={`text-2xl font-bold ${
                  theme === 'dark' ? 'text-orange-400' : 'text-orange-600'
                }`}>
                  {(commentResult.filter_stats?.skipped_time || 0) +
                   (commentResult.filter_stats?.skipped_comments || 0) +
                   (commentResult.filter_stats?.failed || 0)}
                </div>
                <div className={`text-sm ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  跳过视频
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 评论详细列表 */}
        {showCommentDetails && commentResult && commentResult.comments.length > 0 && (
          <div className="mt-6">
            <h4 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              评论详情
            </h4>

            <div className="space-y-4">
              {commentResult.comments.slice(0, 20).map((comment, index) => (
                <div
                  key={comment.cid}
                  className={`p-4 rounded-lg border transition-all duration-300 ${
                    theme === 'dark'
                      ? 'bg-dark-800/50 border-dark-600 hover:border-dark-500'
                      : 'bg-white/50 border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      theme === 'dark' ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600'
                    }`}>
                      {index + 1}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-4 mb-2">
                        <div className="flex items-center space-x-2">
                          <User className={`h-4 w-4 ${
                            theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                          }`} />
                          <span className={`font-medium ${
                            theme === 'dark' ? 'text-white' : 'text-gray-900'
                          }`}>
                            {comment.user_nickname}
                          </span>
                          <span className={`text-sm ${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            @{comment.user_short_id}
                          </span>
                        </div>

                        <div className="flex items-center space-x-1">
                          <Video className={`h-4 w-4 ${
                            theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                          }`} />
                          <span className={`text-xs font-mono ${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            {comment.aweme_id}
                          </span>
                        </div>
                      </div>

                      <div className="mb-3">
                        <p className={`text-sm leading-relaxed ${
                          theme === 'dark' ? 'text-white' : 'text-gray-900'
                        }`}>
                          {comment.text}
                        </p>
                      </div>

                      <div className="flex items-center space-x-4 text-sm">
                        <div className="flex items-center space-x-1">
                          <Heart className={`h-4 w-4 ${
                            theme === 'dark' ? 'text-red-400' : 'text-red-500'
                          }`} />
                          <span className={`${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            {formatNumber(comment.digg_count)}
                          </span>
                        </div>

                        <div className="flex items-center space-x-1">
                          <MessageCircle className={`h-4 w-4 ${
                            theme === 'dark' ? 'text-blue-400' : 'text-blue-500'
                          }`} />
                          <span className={`${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            {formatNumber(comment.reply_count)}
                          </span>
                        </div>

                        <div className="flex items-center space-x-1">
                          <Calendar className={`h-4 w-4 ${
                            theme === 'dark' ? 'text-green-400' : 'text-green-500'
                          }`} />
                          <span className={`${
                            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                          }`}>
                            {formatTime(comment.create_time)}
                          </span>
                        </div>

                        {comment.ip_label && (
                          <div className="flex items-center space-x-1">
                            <MapPin className={`h-4 w-4 ${
                              theme === 'dark' ? 'text-purple-400' : 'text-purple-500'
                            }`} />
                            <span className={`${
                              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                            }`}>
                              {comment.ip_label}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

              {commentResult.comments.length > 20 && (
                <div className={`text-center py-4 ${
                  theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                }`}>
                  显示前20条评论，共{commentResult.comments.length}条评论
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default CommentAnalysisComponent;
