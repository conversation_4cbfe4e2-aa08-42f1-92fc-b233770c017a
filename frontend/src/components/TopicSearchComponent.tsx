import React, { useState } from 'react';
import { Search, Loader2, Video, Heart, MessageCircle, Calendar, FileText } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { douyinApi } from '../services/api';
import { formatTime, formatNumber } from '../utils/formatters';
import { useSimpleNetworkCheck } from '../hooks/useNetworkSecurity';
import type { SearchResponse, VideoInfo } from '../types';
import type { DouyinAccount } from '../utils/multiAccount';

const SearchStatus = {
  IDLE: 'idle',
  SEARCHING: 'searching',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

type SearchStatusType = typeof SearchStatus[keyof typeof SearchStatus];

interface TopicSearchProps {
  currentAccount: DouyinAccount;
  onSearchComplete: (results: SearchResponse, keyword: string) => void;
}



const TopicSearchComponent: React.FC<TopicSearchProps> = ({
  currentAccount,
  onSearchComplete
}) => {
  const { theme } = useTheme();
  const { checkNetworkSecurity } = useSimpleNetworkCheck();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [searchStatus, setSearchStatus] = useState<SearchStatusType>(SearchStatus.IDLE);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);

  const handleSearch = async () => {
    if (!searchKeyword.trim()) {
      setSearchError('请输入搜索话题');
      return;
    }

    if (!currentAccount) {
      setSearchError('用户信息未加载，请刷新页面');
      return;
    }

    const isNetworkSafe = await checkNetworkSecurity();
    if (!isNetworkSafe) {
      setSearchError('当前网络受限制，请切换网络后重试');
      return;
    }

    try {
      setSearchStatus(SearchStatus.SEARCHING);
      setSearchError(null);

      const result = await douyinApi.searchVideos({
        keyword: searchKeyword.trim(),
        pages: 0,
        per_page: 10,
        timeout_seconds: 600
      });

      setSearchStatus(SearchStatus.SUCCESS);
      setSearchResults(result);
      onSearchComplete(result, searchKeyword.trim());

    } catch (error) {
      setSearchStatus(SearchStatus.ERROR);
      setSearchError(error instanceof Error ? error.message : '搜索失败，请稍后重试');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && searchStatus !== SearchStatus.SEARCHING) {
      handleSearch();
    }
  };

  return (
    <>
      <Card variant="glass" padding="lg" className="mb-8">
        <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          话题搜索
        </h3>

        <div className="space-y-4">
          <div className="flex space-x-3">
            <div className="flex-1">
              <input
                type="text"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="输入搜索话题关键词..."
                disabled={searchStatus === SearchStatus.SEARCHING}
                className={`w-full px-4 py-3 rounded-xl border transition-all duration-300 ${
                  theme === 'dark'
                    ? 'bg-dark-800 border-dark-600 text-white placeholder-dark-400 focus:border-blue-500'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={searchStatus === SearchStatus.SEARCHING || !searchKeyword.trim()}
              className="bg-blue-500 hover:bg-blue-600 px-6 flex items-center space-x-2"
            >
              {searchStatus === SearchStatus.SEARCHING ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              <span>{searchStatus === SearchStatus.SEARCHING ? '搜索中...' : '搜索'}</span>
            </Button>
          </div>

          {searchError && (
            <div className={`p-3 rounded-lg ${
              theme === 'dark' ? 'bg-red-900/20 text-red-400' : 'bg-red-50 text-red-600'
            }`}>
              {searchError}
            </div>
          )}
        </div>
      </Card>


      {searchResults && searchStatus === SearchStatus.SUCCESS && (
      <Card variant="glass" padding="lg" className="mt-6">
        <div className="mb-4">
          <h4 className={`text-lg font-semibold mb-2 transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            搜索结果
          </h4>
          <div className={`text-sm ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            关键词：{searchResults.keyword} | 共获取 {searchResults.result.videos.length} 个视频
          </div>
        </div>

        <div className="space-y-4">
          {searchResults.result.videos.map((video: VideoInfo, index: number) => (
            <div
              key={video.aweme_id}
              className={`p-4 rounded-lg border transition-all duration-300 ${
                theme === 'dark'
                  ? 'bg-dark-800/50 border-dark-600 hover:border-dark-500'
                  : 'bg-white/50 border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-start space-x-4">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  theme === 'dark' ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600'
                }`}>
                  {index + 1}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-4 mb-2">
                    <div className="flex items-center space-x-1">
                      <Video className={`h-4 w-4 ${
                        theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                      }`} />
                      <span className={`text-xs font-mono ${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`}>
                        {video.aweme_id}
                      </span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Heart className={`h-4 w-4 ${
                          theme === 'dark' ? 'text-red-400' : 'text-red-500'
                        }`} />
                        <span className={`text-sm ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          {formatNumber(video.statistics?.digg_count)}
                        </span>
                      </div>

                      <div className="flex items-center space-x-1">
                        <MessageCircle className={`h-4 w-4 ${
                          theme === 'dark' ? 'text-blue-400' : 'text-blue-500'
                        }`} />
                        <span className={`text-sm ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          {formatNumber(video.statistics?.comment_count)}
                        </span>
                      </div>

                      <div className="flex items-center space-x-1">
                        <Calendar className={`h-4 w-4 ${
                          theme === 'dark' ? 'text-green-400' : 'text-green-500'
                        }`} />
                        <span className={`text-sm ${
                          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                        }`}>
                          {formatTime(video.create_time, { includeSeconds: false })}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-2">
                    <FileText className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                      theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                    }`} />
                    <p className={`text-sm leading-relaxed ${
                      theme === 'dark' ? 'text-white' : 'text-gray-900'
                    }`}>
                      {video.desc || '无描述'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    )}
    </>
  );
};

export default TopicSearchComponent;
