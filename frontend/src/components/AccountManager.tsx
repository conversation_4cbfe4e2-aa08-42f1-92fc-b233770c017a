import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { User, Plus, Trash2, CheckCircle, AlertCircle, RefreshCw, Crown } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import { useNavigate } from 'react-router-dom';
import Card from './Card';
import Button from './Button';
import { MultiAccountManager, type DouyinAccount } from '../utils/multiAccount';
import { formatTimeString } from '../utils/formatters';
interface AccountManagerProps {
  isDeveloper: boolean;
}

const AccountManager: React.FC<AccountManagerProps> = ({
  isDeveloper
}) => {
  const { theme } = useTheme();
  const navigate = useNavigate();
  const [accounts, setAccounts] = useState<DouyinAccount[]>([]);
  const [validatingAccounts, setValidatingAccounts] = useState<Set<string>>(new Set());
  const [notification, setNotification] = useState<{ message: string; type: 'error' | 'success' | 'warning' } | null>(null);

  const loadAccounts = useCallback(() => {
    const accountList = MultiAccountManager.getAccounts();
    setAccounts(accountList);
  }, []);

  useEffect(() => {
    MultiAccountManager.migrateFromSingleAccount();
    loadAccounts();
  }, [loadAccounts]);


  useEffect(() => {
    if (notification) {
      const timer = setTimeout(() => {
        setNotification(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [notification]);



  const handleRemoveAccount = useCallback((accountId: string) => {
    if (window.confirm('确定要删除这个账号吗？此操作不可撤销。')) {
      const success = MultiAccountManager.removeAccount(accountId);
      if (success) {
        loadAccounts();
        setNotification({ message: '账号删除成功', type: 'success' });
      }
    }
  }, [loadAccounts]);

  const handleValidateAccount = async (accountId: string) => {
    setValidatingAccounts(prev => new Set(prev).add(accountId));

    try {
      const isValid = await MultiAccountManager.validateAccount(accountId);
      loadAccounts();

      if (!isValid) {
        setNotification({ message: '账号验证失败，请重新登录该账号', type: 'error' });
      }
    } catch {
      setNotification({ message: '验证账号时发生错误', type: 'error' });
    } finally {
      setValidatingAccounts(prev => {
        const newSet = new Set(prev);
        newSet.delete(accountId);
        return newSet;
      });
    }
  };

  const handleAddAccount = () => {
    const { canAdd, reason } = MultiAccountManager.canAddAccount(isDeveloper);

    if (!canAdd) {
      setNotification({ message: reason || '无法添加账号', type: 'warning' });
      return;
    }

    navigate('/login/douyin?mode=add');
  };





  const themeClasses = useMemo(() => ({
    title: theme === 'dark' ? 'text-white text-xl font-semibold' : 'text-gray-900 text-xl font-semibold',
    subtitle: theme === 'dark' ? 'text-dark-300 text-sm' : 'text-gray-500 text-sm',
    emptyState: theme === 'dark' ? 'text-dark-300' : 'text-gray-500',
    accountName: theme === 'dark' ? 'text-white' : 'text-gray-900',
    accountUid: theme === 'dark' ? 'text-dark-300' : 'text-gray-500',
    timeText: theme === 'dark' ? 'text-dark-400' : 'text-gray-400',
    statsText: theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
  }), [theme]);

  return (
    <Card variant="glass" padding="lg">
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-xl">
              <User className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <h3 className={themeClasses.title}>
                账号管理
                {isDeveloper && (
                  <Crown className="inline-block ml-2 h-5 w-5 text-yellow-400" />
                )}
              </h3>
              <p className={themeClasses.subtitle}>
                {isDeveloper ? '开发者可添加多个账号' : '普通用户限制一个账号'}
              </p>
            </div>
          </div>
          
          <Button
            onClick={handleAddAccount}
            className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>添加账号</span>
          </Button>
        </div>


        {accounts.length === 0 ? (
          <div className={`text-center py-8 ${themeClasses.emptyState}`}>
            <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>暂无账号，请添加一个抖音账号开始使用</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {accounts.map((account) => (
              <Card
                key={account.id}
                variant="glass"
              >
                <div className="flex items-center space-x-4 p-4">

                  <div className="relative">
                    {account.avatar ? (
                      <img
                        src={account.avatar}
                        alt={account.nickname}
                        className="w-12 h-12 rounded-full object-cover"
                        loading="lazy"
                        decoding="async"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full">
                        <User className="h-6 w-6 text-white" />
                      </div>
                    )}
                  </div>


                  <div className="flex-1 min-w-0">
                    <h4 className={`font-semibold truncate ${themeClasses.accountName}`}>
                      {account.nickname}
                    </h4>
                    <p className={`text-sm truncate ${themeClasses.accountUid}`}>
                      UID: {account.uid}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      {account.isValid ? (
                        <div className="flex items-center space-x-1">
                          <CheckCircle className="h-3 w-3 text-green-400" />
                          <span className="text-xs text-green-400">登录有效</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-1">
                          <AlertCircle className="h-3 w-3 text-red-400" />
                          <span className="text-xs text-red-400">登录失效</span>
                        </div>
                      )}
                      <span className={`text-xs ${themeClasses.timeText}`}>
                        {account.lastValidation && formatTimeString(account.lastValidation)}
                      </span>
                    </div>
                  </div>


                  <div className="flex flex-col space-y-2">
                    <Button
                      onClick={() => handleValidateAccount(account.id)}
                      disabled={validatingAccounts.has(account.id)}
                      className="bg-blue-500 hover:bg-blue-600 text-xs px-3 py-1 flex items-center space-x-1"
                    >
                      {validatingAccounts.has(account.id) ? (
                        <RefreshCw className="h-3 w-3 animate-spin" />
                      ) : (
                        <RefreshCw className="h-3 w-3" />
                      )}
                      <span>验证</span>
                    </Button>



                    <Button
                      onClick={() => handleRemoveAccount(account.id)}
                      className="bg-red-500 hover:bg-red-600 text-xs px-3 py-1 flex items-center space-x-1"
                    >
                      <Trash2 className="h-3 w-3" />
                      <span>删除</span>
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}


        <div className={`text-sm ${themeClasses.statsText}`}>
          <p>
            当前共有 <span className="font-semibold text-blue-400">{accounts.length}</span> 个账号
            {!isDeveloper && accounts.length > 0 && (
              <span className="ml-2 text-orange-400">（普通用户限制）</span>
            )}
          </p>
        </div>
      </div>




      {/* 通知提示 */}
      {notification && (
        <div className={`mt-4 p-3 rounded-lg border ${
          notification.type === 'error'
            ? 'bg-red-500/10 border-red-500/50 text-red-400'
            : notification.type === 'warning'
            ? 'bg-yellow-500/10 border-yellow-500/50 text-yellow-400'
            : 'bg-green-500/10 border-green-500/50 text-green-400'
        }`}>
          <div className="flex items-center justify-between">
            <span className="text-sm">{notification.message}</span>
            <button
              onClick={() => setNotification(null)}
              className="ml-2 text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </Card>
  );
};

export default AccountManager;
