import React from 'react';
import { useTheme } from '../hooks/useTheme';

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

const Loading: React.FC<LoadingProps> = ({
  size = 'md',
  text,
  className = '',
}) => {
  const { theme } = useTheme();

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative">
        <div className={`${sizeClasses[size]} animate-spin rounded-full border-b-2 border-primary-500 mx-auto`} />
      </div>
      {text && (
        <p className={`mt-2 text-sm animate-pulse ${
          theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
        }`}>
          {text}
        </p>
      )}
    </div>
  );
};

export default Loading;
