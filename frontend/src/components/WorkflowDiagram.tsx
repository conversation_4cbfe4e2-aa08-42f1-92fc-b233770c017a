import React, { useCallback } from 'react';
import ReactFlow, {
  addEdge,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  type Node,
  type Edge,
  type Connection,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { Target, MessageSquare, BarChart3, Users, Database, ChartBar } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';

// 节点数据类型
interface NodeData {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  isStart?: boolean;
  isEnd?: boolean;
}

// 自定义节点组件
const CustomNode = ({ data }: { data: NodeData }) => {
  const { theme } = useTheme();
  const { label, icon: Icon, color, isStart, isEnd } = data;

  const nodeClass = isStart
    ? `px-8 py-5 rounded-xl border-3 transition-all duration-300 hover:scale-105 ${
        theme === 'dark'
          ? 'bg-red-900/20 border-red-500 text-white shadow-2xl ring-2 ring-red-500/30'
          : 'bg-red-50 border-red-500 text-gray-900 shadow-2xl ring-2 ring-red-500/30'
      }`
    : isEnd
    ? `px-6 py-3 rounded-full transition-all duration-300 hover:scale-105 ${
        theme === 'dark'
          ? 'bg-gray-800 border-2 border-gray-600 text-white shadow-lg'
          : 'bg-white border-2 border-gray-300 text-gray-900 shadow-lg'
      }`
    : `px-6 py-4 rounded-xl border-2 transition-all duration-300 hover:scale-105 ${
        theme === 'dark'
          ? 'bg-gray-800 border-gray-600 text-white shadow-xl'
          : 'bg-white border-gray-300 text-gray-900 shadow-xl'
      }`;

  return (
    <div className={nodeClass}>
      <div className="flex items-center space-x-3">
        {Icon && <Icon className={`${isStart ? 'h-6 w-6' : 'h-5 w-5'} ${color}`} />}
        <span className={`font-${isStart ? 'bold text-lg' : 'medium'}`}>{label}</span>
      </div>
    </div>
  );
};

// 节点类型定义
const nodeTypes = {
  custom: CustomNode,
};

// 初始节点
const initialNodes: Node[] = [
  {
    id: '1',
    type: 'custom',
    position: { x: 400, y: 50 },
    data: { 
      label: 'Planner', 
      icon: Target, 
      color: 'text-red-500',
      isStart: true 
    },
  },
  {
    id: '2',
    type: 'custom',
    position: { x: 200, y: 200 },
    data: { 
      label: 'Human Feedback', 
      icon: MessageSquare, 
      color: 'text-yellow-500' 
    },
  },
  {
    id: '3',
    type: 'custom',
    position: { x: 600, y: 200 },
    data: { 
      label: 'Reporter', 
      icon: BarChart3, 
      color: 'text-cyan-500' 
    },
  },
  {
    id: '4',
    type: 'custom',
    position: { x: 400, y: 350 },
    data: { 
      label: 'Research Team', 
      icon: Users, 
      color: 'text-green-500' 
    },
  },
  {
    id: '5',
    type: 'custom',
    position: { x: 200, y: 500 },
    data: { 
      label: 'Researcher', 
      icon: Database, 
      color: 'text-purple-500' 
    },
  },
  {
    id: '6',
    type: 'custom',
    position: { x: 600, y: 500 },
    data: { 
      label: 'Coder', 
      icon: ChartBar, 
      color: 'text-orange-500' 
    },
  },
  {
    id: '7',
    type: 'custom',
    position: { x: 400, y: 650 },
    data: { 
      label: 'End', 
      isEnd: true 
    },
  },
];

// 初始连接线
const initialEdges: Edge[] = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e2-4',
    source: '2',
    target: '4',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e3-4',
    source: '3',
    target: '4',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e4-5',
    source: '4',
    target: '5',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e4-6',
    source: '4',
    target: '6',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e5-7',
    source: '5',
    target: '7',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  {
    id: 'e6-7',
    source: '6',
    target: '7',
    style: {
      stroke: '#3b82f6',
      strokeWidth: 2
    }
  },
  // 反馈循环 - 特殊样式
  {
    id: 'e3-1',
    source: '3',
    target: '1',
    style: {
      stroke: '#f59e0b',
      strokeWidth: 2
    }
  },
];

interface WorkflowDiagramProps {
  className?: string;
}

const WorkflowDiagram: React.FC<WorkflowDiagramProps> = ({ className = '' }) => {
  const { theme } = useTheme();
  const [nodes, , onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  const onConnect = useCallback(
    (params: Edge | Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  // 根据主题动态调整边缘颜色
  const themedEdges = edges.map(edge => ({
    ...edge,
    style: {
      ...edge.style,
      stroke: edge.id === 'e3-1'
        ? '#f59e0b' // 反馈循环保持橙色
        : theme === 'dark' ? '#60a5fa' : '#3b82f6' // 根据主题调整蓝色
    }
  }));

  return (
    <div className={`h-96 rounded-2xl border transition-all duration-300 ${className} ${
      theme === 'dark'
        ? 'bg-gray-900 border-gray-700'
        : 'bg-white border-gray-200'
    }`}>
      <ReactFlow
        nodes={nodes}
        edges={themedEdges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-left"
        proOptions={{ hideAttribution: true }}
      >
        <Controls />
        <Background
          variant={BackgroundVariant.Dots}
          gap={12}
          size={1}
          color={theme === 'dark' ? '#374151' : '#e5e7eb'}
        />
      </ReactFlow>
    </div>
  );
};

export default WorkflowDiagram;
