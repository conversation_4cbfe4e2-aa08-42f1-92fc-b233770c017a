import React, { useState } from 'react';
import { Users, Download, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import { douyinApi } from '../services/api';
import type { CommentCustomerAnalysisResult, DouyinUserInfo } from '../types';
import type { DouyinAccount } from '../utils/multiAccount';

const BatchSignatureStatus = {
  IDLE: 'idle',
  FETCHING: 'fetching',
  SUCCESS: 'success',
  ERROR: 'error'
} as const;

type BatchSignatureStatusType = typeof BatchSignatureStatus[keyof typeof BatchSignatureStatus];

interface BatchUserSignatureResult {
  signatures: DouyinUserInfo[];
  failed: string[];
  total: number;
  success: number;
  errors: Array<{
    secUids: string[];
    error: string;
    batchIndex: number;
  }>;
  processingTime: number;
}

interface BatchUserSignatureProps {
  customerAnalysisResult: CommentCustomerAnalysisResult | null;
  selectedAccount: DouyinAccount | null;
}

const BatchUserSignatureComponent: React.FC<BatchUserSignatureProps> = ({
  customerAnalysisResult,
  selectedAccount
}) => {
  const { theme } = useTheme();
  const [status, setStatus] = useState<BatchSignatureStatusType>(BatchSignatureStatus.IDLE);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<BatchUserSignatureResult | null>(null);
  const [progress, setProgress] = useState({ current: 0, total: 0 });

  const handleBatchGetSignatures = async () => {
    if (!customerAnalysisResult || !selectedAccount) {
      setError('缺少必要的数据或账号信息');
      return;
    }

    const potentialCustomers = customerAnalysisResult.potential_customers || [];
    if (potentialCustomers.length === 0) {
      setError('没有找到潜在客户数据');
      return;
    }

    const startTime = Date.now();
    
    try {
      setStatus(BatchSignatureStatus.FETCHING);
      setError(null);
      setResult(null);

      // 提取sec_uid列表
      const secUids = potentialCustomers
        .map(customer => customer.user_sec_uid)
        .filter(uid => uid && uid.trim());

      setProgress({ current: 0, total: secUids.length });

      // 分批处理，每批最多10个
      const chunkArray = <T,>(array: T[], chunkSize: number): T[][] => {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += chunkSize) {
          chunks.push(array.slice(i, i + chunkSize));
        }
        return chunks;
      };

      const batches = chunkArray(secUids, 10);
      const batchResult: BatchUserSignatureResult = {
        signatures: [],
        failed: [],
        total: secUids.length,
        success: 0,
        errors: [],
        processingTime: 0
      };

      // 逐批处理
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        
        try {
          const result = await douyinApi.getUserInfo({
            sec_uid: batch,
            cookies: selectedAccount.cookies,
            timeout_seconds: 10
          });

          if (result.users) {
            batchResult.signatures.push(...result.users);
            setProgress({ current: batchResult.signatures.length, total: secUids.length });
          }

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '未知错误';
          batchResult.failed.push(...batch);
          batchResult.errors.push({
            secUids: batch,
            error: errorMessage,
            batchIndex: i
          });
        }

        // 批次间延迟，避免请求过于频繁
        if (i < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      batchResult.success = batchResult.signatures.length;
      batchResult.processingTime = Date.now() - startTime;
      
      setResult(batchResult);
      setStatus(BatchSignatureStatus.SUCCESS);

    } catch (error) {
      setStatus(BatchSignatureStatus.ERROR);
      const errorMessage = error instanceof Error ? error.message : '批量获取用户签名失败';
      setError(errorMessage);
    }
  };

  const downloadSignatureResult = () => {
    if (!result || !customerAnalysisResult) return;

    // 合并客户分析结果和签名数据
    const mergedData = customerAnalysisResult.potential_customers.map(customer => {
      const signatureInfo = result.signatures.find(sig => sig.sec_uid === customer.user_sec_uid);
      return {
        ...customer,
        signature: signatureInfo?.signature || '未获取到签名',
        avatar_url: signatureInfo?.avatar_url || '',
        fetch_status: signatureInfo ? '成功' : '失败'
      };
    });

    const csvContent = [
      ['用户昵称', '抖音号', 'SecUID', '用户ID', '个性签名', '意向类型', '置信度', '分析原因', '获取状态', '头像URL'].join(','),
      ...mergedData.map(user => [
        `"${user.user_nickname}"`,
        user.user_short_id,
        user.user_sec_uid,
        user.user_uid,
        `"${user.signature.replace(/"/g, '""')}"`,
        user.intent_type,
        user.confidence.toString(),
        `"${user.reasoning.replace(/"/g, '""')}"`,
        user.fetch_status,
        user.avatar_url || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `customer_signatures_${new Date().getTime()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!customerAnalysisResult || !customerAnalysisResult.potential_customers?.length) {
    return null;
  }

  const potentialCustomersCount = customerAnalysisResult.potential_customers.length;

  return (
    <Card variant="glass" padding="lg" className="mb-8">
      <div className="flex items-center space-x-3 mb-4">
        <div className="flex items-center justify-center w-10 h-10 bg-green-500/20 rounded-xl">
          <Users className="h-5 w-5 text-green-400" />
        </div>
        <div>
          <h3 className={`text-lg font-semibold transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            批量获取用户签名
          </h3>
          <p className={`text-sm transition-colors duration-300 ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            获取 {potentialCustomersCount} 个潜在客户的个性签名
          </p>
        </div>
      </div>

      {/* 统计信息 */}
      <div className={`grid grid-cols-2 md:grid-cols-4 gap-4 mb-6 p-4 rounded-lg ${
        theme === 'dark' ? 'bg-dark-800' : 'bg-gray-50'
      }`}>
        <div className="text-center">
          <div className={`text-2xl font-bold ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            {potentialCustomersCount}
          </div>
          <div className={`text-sm ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            待获取用户
          </div>
        </div>
        
        {result && (
          <>
            <div className="text-center">
              <div className={`text-2xl font-bold text-green-500`}>
                {result.success}
              </div>
              <div className={`text-sm ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                成功获取
              </div>
            </div>
            
            <div className="text-center">
              <div className={`text-2xl font-bold text-red-500`}>
                {result.failed.length}
              </div>
              <div className={`text-sm ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                获取失败
              </div>
            </div>
            
            <div className="text-center">
              <div className={`text-2xl font-bold ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {(result.processingTime / 1000).toFixed(1)}s
              </div>
              <div className={`text-sm ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                处理时间
              </div>
            </div>
          </>
        )}
      </div>

      {/* 进度条 */}
      {status === BatchSignatureStatus.FETCHING && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className={`text-sm ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              获取进度: {progress.current} / {progress.total}
            </span>
            <span className={`text-sm ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              {progress.total > 0 ? Math.round((progress.current / progress.total) * 100) : 0}%
            </span>
          </div>
          <div className={`w-full bg-gray-200 rounded-full h-2 ${
            theme === 'dark' ? 'bg-dark-600' : 'bg-gray-200'
          }`}>
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ 
                width: progress.total > 0 ? `${(progress.current / progress.total) * 100}%` : '0%' 
              }}
            />
          </div>
        </div>
      )}

      {/* 操作按钮 */}
      <div className="flex space-x-3">
        <Button
          onClick={handleBatchGetSignatures}
          disabled={status === BatchSignatureStatus.FETCHING || !selectedAccount}
          className="bg-green-500 hover:bg-green-600 flex items-center space-x-2"
        >
          {status === BatchSignatureStatus.FETCHING ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Users className="h-4 w-4" />
          )}
          <span>
            {status === BatchSignatureStatus.FETCHING ? '获取中...' : '批量获取签名'}
          </span>
        </Button>

        {result && (
          <Button
            onClick={downloadSignatureResult}
            className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>下载签名结果</span>
          </Button>
        )}
      </div>

      {/* 状态消息 */}
      {status === BatchSignatureStatus.SUCCESS && result && (
        <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
          theme === 'dark' ? 'bg-green-900/20 border border-green-700' : 'bg-green-50 border border-green-200'
        }`}>
          <CheckCircle className="h-5 w-5 text-green-500" />
          <span className={`text-sm ${
            theme === 'dark' ? 'text-green-300' : 'text-green-800'
          }`}>
            批量获取完成！成功获取 {result.success} 个用户签名，失败 {result.failed.length} 个
          </span>
        </div>
      )}

      {error && (
        <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
          theme === 'dark' ? 'bg-red-900/20 border border-red-700' : 'bg-red-50 border border-red-200'
        }`}>
          <AlertCircle className="h-5 w-5 text-red-500" />
          <span className={`text-sm ${
            theme === 'dark' ? 'text-red-300' : 'text-red-800'
          }`}>
            {error}
          </span>
        </div>
      )}

      {!selectedAccount && (
        <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
          theme === 'dark' ? 'bg-yellow-900/20 border border-yellow-700' : 'bg-yellow-50 border border-yellow-200'
        }`}>
          <AlertCircle className="h-5 w-5 text-yellow-500" />
          <span className={`text-sm ${
            theme === 'dark' ? 'text-yellow-300' : 'text-yellow-800'
          }`}>
            请先选择一个有效的账号来获取用户签名
          </span>
        </div>
      )}
    </Card>
  );
};

export default BatchUserSignatureComponent;
