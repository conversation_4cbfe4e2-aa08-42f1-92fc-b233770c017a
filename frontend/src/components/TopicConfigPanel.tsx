import React, { useState, useEffect } from 'react';
import { Plus, Trash2, Hash, Settings } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import Button from './Button';
import type { TopicConfig, SearchConfig } from '../types';

interface TopicConfigPanelProps {
  topics: TopicConfig[];
  onTopicsChange: (topics: TopicConfig[]) => void;
  searchConfig: SearchConfig;
  onSearchConfigChange: (config: SearchConfig) => void;
}

const TopicConfigPanel: React.FC<TopicConfigPanelProps> = ({
  topics,
  onTopicsChange,
  searchConfig,
  onSearchConfigChange
}) => {
  const { theme } = useTheme();
  const [newTopic, setNewTopic] = useState('');

  useEffect(() => {
    const savedConfig = localStorage.getItem('liandong_search_config');
    if (savedConfig) {
      try {
        const config = JSON.parse(savedConfig);
        onSearchConfigChange(config);
      } catch (error) {
        // 配置加载失败，使用默认配置
      }
    }
  }, [onSearchConfigChange]);

  const handleSearchConfigChange = (newConfig: SearchConfig) => {
    onSearchConfigChange(newConfig);
    localStorage.setItem('liandong_search_config', JSON.stringify(newConfig));
  };

  const handleAddTopic = () => {
    const keyword = newTopic.trim();
    if (!keyword || topics.some(topic => topic.keyword === keyword)) {
      return;
    }

    const newTopicItem: TopicConfig = {
      id: Date.now().toString(),
      keyword
    };

    onTopicsChange([...topics, newTopicItem]);
    setNewTopic('');
  };

  const handleRemoveTopic = (index: number) => {
    const updatedTopics = topics.filter((_, i) => i !== index);
    onTopicsChange(updatedTopics);
  };

  return (
    <div className="space-y-6">
      <Card variant="glass">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-orange-500/20 rounded-xl">
              <Settings className="h-5 w-5 text-orange-400" />
            </div>
            <div>
              <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                搜索配置
              </h3>
              <p className={`text-sm transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                配置每个话题的搜索页数和每页数量
              </p>
            </div>
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            目标数量
          </label>
          <input
            type="number"
            min="1"
            max="10000"
            value={searchConfig.totalCount}
            onChange={(e) => handleSearchConfigChange({
              totalCount: Math.max(1, parseInt(e.target.value) || 100)
            })}
            placeholder="例如：1000"
            className={`w-full px-4 py-2 rounded-lg border transition-all duration-300 ${
              theme === 'dark'
                ? 'bg-dark-800 border-dark-600 text-white focus:border-orange-500'
                : 'bg-white border-gray-300 text-gray-900 focus:border-orange-500'
            } focus:outline-none focus:ring-2 focus:ring-orange-500/20`}
          />
        </div>
      </Card>

      <Card variant="glass">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-xl">
              <Hash className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                话题配置
              </h3>
              <p className={`text-sm transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                配置要搜索的话题关键词
              </p>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full text-sm ${
            theme === 'dark' ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600'
          }`}>
            {topics.length} 个话题
          </div>
        </div>

      <div className="flex space-x-3 mb-4">
        <input
          type="text"
          value={newTopic}
          onChange={(e) => setNewTopic(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && handleAddTopic()}
          placeholder="输入新的搜索话题..."
          className={`flex-1 px-4 py-2 rounded-lg border transition-all duration-300 ${
            theme === 'dark'
              ? 'bg-dark-800 border-dark-600 text-white placeholder-dark-400 focus:border-blue-500'
              : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
          } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
        />
        <Button
          onClick={handleAddTopic}
          disabled={!newTopic.trim() || topics.some(topic => topic.keyword === newTopic.trim())}
          className="bg-blue-500 hover:bg-blue-600 flex items-center space-x-2"
        >
          <Plus className="h-4 w-4" />
          <span>添加</span>
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {topics.map((topic, index) => (
          <div
            key={topic.id}
            className={`p-4 rounded-lg border transition-all duration-300 ${
              theme === 'dark'
                ? 'bg-dark-800/50 border-dark-600 hover:border-dark-500'
                : 'bg-white border-gray-200 hover:border-gray-300'
            } hover:shadow-md`}
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <Hash className="h-4 w-4 text-blue-400" />
                <span className={`font-medium transition-colors duration-300 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>
                  {topic.keyword}
                </span>
              </div>
              <Button
                onClick={() => handleRemoveTopic(index)}
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>
      </Card>
    </div>
  );
};

export default TopicConfigPanel;
