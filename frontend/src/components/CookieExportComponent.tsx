import React, { useState } from 'react';
import { Copy, Check, Download, Users } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Button from './Button';
import Card from './Card';
import { MultiAccountManager } from '../utils/multiAccount';
import type { CookieData } from '../types';

const CookieExportComponent: React.FC = () => {
  const { theme } = useTheme();
  const [copied, setCopied] = useState(false);

  const mergeCookies = (loginCookies: CookieData[]): CookieData[] => {
    return loginCookies;
  };

  const formatAllAccountsForExport = (): string => {
    const allAccounts = MultiAccountManager.getAccounts();
    
    const exportData = {
      exportDate: new Date().toISOString(),
      totalAccounts: allAccounts.length,
      accounts: allAccounts.map(account => {
        const allCookies = mergeCookies(account.cookies);
        
        return {
          accountId: account.id,
          nickname: account.nickname,
          uid: account.uid,
          avatar: account.avatar,
          sessionId: account.sessionId,
          addedAt: account.addedAt,
          lastValidation: account.lastValidation,
          isValid: account.isValid,
          cookies: allCookies,
          userInfo: account.userInfo
        };
      })
    };
    
    return JSON.stringify(exportData, null, 2);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      
      // 3秒后清除复制状态
      setTimeout(() => {
        setCopied(false);
      }, 3000);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const handleCopyAllCookies = () => {
    const exportData = formatAllAccountsForExport();
    copyToClipboard(exportData);
  };

  const downloadAllCookiesFile = () => {
    const exportData = formatAllAccountsForExport();
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `all_accounts_cookies_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const allAccounts = MultiAccountManager.getAccounts();

  if (allAccounts.length === 0) {
    return null;
  }

  return (
    <Card variant="glass" padding="md" className="mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-lg">
            <Users className="h-5 w-5 text-blue-400" />
          </div>
          <div>
            <h4 className={`text-lg font-semibold transition-colors duration-300 ${
              theme === 'dark' ? 'text-white' : 'text-gray-900'
            }`}>
              账号迁移
            </h4>
            <p className={`text-sm transition-colors duration-300 ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
            }`}>
              一键导出所有账号数据
            </p>
          </div>
        </div>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          theme === 'dark' 
            ? 'bg-green-500/20 text-green-400' 
            : 'bg-green-100 text-green-700'
        }`}>
          {allAccounts.length} 个账号
        </div>
      </div>

      <div className="flex items-center justify-between p-4 rounded-lg bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20">
        <div className="flex-1">
          <p className={`font-medium transition-colors duration-300 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}>
            所有账号完整数据
          </p>
          <p className={`text-sm transition-colors duration-300 ${
            theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
          }`}>
            包含 {allAccounts.length} 个账号的登录+搜索Cookie，用户信息，会话数据等
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button
            onClick={handleCopyAllCookies}
            className={`flex items-center space-x-2 ${
              copied 
                ? 'bg-green-500 hover:bg-green-600' 
                : 'bg-blue-500 hover:bg-blue-600'
            }`}
            size="sm"
          >
            {copied ? (
              <>
                <Check className="h-4 w-4" />
                <span>已复制</span>
              </>
            ) : (
              <>
                <Copy className="h-4 w-4" />
                <span>复制数据</span>
              </>
            )}
          </Button>
          <Button
            onClick={downloadAllCookiesFile}
            className="bg-gray-500 hover:bg-gray-600 flex items-center space-x-2"
            size="sm"
          >
            <Download className="h-4 w-4" />
            <span>下载文件</span>
          </Button>
        </div>
      </div>

      {/* 账号预览 */}
      <div className="mt-4 space-y-2">
        <p className={`text-sm font-medium transition-colors duration-300 ${
          theme === 'dark' ? 'text-white' : 'text-gray-900'
        }`}>
          将导出以下账号:
        </p>
        <div className="flex flex-wrap gap-2">
          {allAccounts.map((account) => (
            <div
              key={account.id}
              className={`flex items-center space-x-2 px-3 py-1 rounded-full text-xs ${
                theme === 'dark'
                  ? 'bg-gray-700 text-gray-300'
                  : 'bg-gray-100 text-gray-700'
              }`}
            >
              {account.avatar ? (
                <img
                  src={account.avatar}
                  alt={account.nickname}
                  className="w-4 h-4 rounded-full"
                />
              ) : (
                <div className="w-4 h-4 rounded-full bg-gray-400" />
              )}
              <span>{account.nickname}</span>
            </div>
          ))}
        </div>
      </div>

      {/* 使用说明 */}
      <div className={`mt-4 p-3 rounded-lg border-l-4 border-blue-500 ${
        theme === 'dark' 
          ? 'bg-blue-500/10 border-blue-400' 
          : 'bg-blue-50 border-blue-500'
      }`}>
        <p className={`text-sm transition-colors duration-300 ${
          theme === 'dark' ? 'text-blue-300' : 'text-blue-700'
        }`}>
          <strong>使用说明：</strong>
          复制或下载后，可在新设备上通过账号管理功能批量导入，实现所有账号的快速迁移。
        </p>
      </div>
    </Card>
  );
};

export default CookieExportComponent;