import React, { useState, useEffect } from 'react';
import { User, CheckCircle, AlertCircle, Crown } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Card from './Card';
import { MultiAccountManager, type DouyinAccount } from '../utils/multiAccount';

interface UserSelectorProps {
  isDeveloper: boolean;
  onUserChange: (selectedUsers: DouyinAccount[]) => void;
  multiSelectMode?: boolean;
  accountFilter?: (account: DouyinAccount) => boolean;
  showSearchCookieStatus?: boolean;
  title?: string;
  description?: string;
}

const UserSelector: React.FC<UserSelectorProps> = ({
  isDeveloper,
  onUserChange,
  multiSelectMode = false,
  accountFilter,
  showSearchCookieStatus = false,
  title = "用户选择",
  description
}) => {
  const { theme } = useTheme();
  const [accounts, setAccounts] = useState<DouyinAccount[]>([]);
  const [selectedAccountIds, setSelectedAccountIds] = useState<string[]>([]);


  useEffect(() => {
    loadAccounts();
  }, []);

  useEffect(() => {
    const selectedAccounts = accounts.filter(account =>
      selectedAccountIds.includes(account.id)
    );
    onUserChange(selectedAccounts);
  }, [selectedAccountIds, accounts, onUserChange]);

  const loadAccounts = () => {
    const accountList = MultiAccountManager.getAccounts();
    const currentId = MultiAccountManager.getCurrentAccountId();

    // 应用账号过滤器
    const filteredAccounts = accountFilter ? accountList.filter(accountFilter) : accountList;
    setAccounts(filteredAccounts);

    // 初始化选中状态
    if (currentId && filteredAccounts.some(acc => acc.id === currentId)) {
      setSelectedAccountIds([currentId]);
    } else if (filteredAccounts.length > 0) {
      setSelectedAccountIds([filteredAccounts[0].id]);
    }
  };

  const handleAccountSelect = (accountId: string) => {
    if (multiSelectMode && isDeveloper) {
      // 多选模式（仅开发者）
      setSelectedAccountIds(prev => {
        if (prev.includes(accountId)) {
          return prev.filter(id => id !== accountId);
        } else {
          return [...prev, accountId];
        }
      });
    } else {
      // 单选模式
      setSelectedAccountIds([accountId]);

      // 更新当前账号
      MultiAccountManager.setCurrentAccount(accountId);
    }
  };



  const getAccountStatusIcon = (account: DouyinAccount) => {
    if (showSearchCookieStatus) {
      if (account.isValid) {
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      } else {
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      }
    } else {
      // 显示账号验证状态
      if (account.isValid) {
        return <CheckCircle className="h-4 w-4 text-green-400" />;
      } else {
        return <AlertCircle className="h-4 w-4 text-red-400" />;
      }
    }
  };

  return (
    <Card variant="glass" padding="lg" className="mb-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-500/20 rounded-xl">
              <User className="h-5 w-5 text-blue-400" />
            </div>
            <div>
              <h3 className={`text-lg font-semibold transition-colors duration-300 ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                {title}
                {isDeveloper && (
                  <Crown className="inline-block ml-2 h-5 w-5 text-yellow-400" />
                )}
              </h3>
              <p className={`text-sm transition-colors duration-300 ${
                theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
              }`}>
                {description || (multiSelectMode && isDeveloper
                  ? '开发者可选择多个用户进行批量操作'
                  : '选择要使用的用户账号'
                )}
              </p>
            </div>
          </div>

          {multiSelectMode && isDeveloper && (
            <div className={`text-sm px-3 py-1 rounded-full ${
              theme === 'dark' ? 'bg-blue-900/30 text-blue-400' : 'bg-blue-100 text-blue-600'
            }`}>
              多选模式
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {accounts.length === 0 ? (
            <div className={`col-span-full p-8 text-center ${
              theme === 'dark' ? 'text-dark-300' : 'text-gray-500'
            }`}>
              暂无可用账号
            </div>
          ) : (
            accounts.map((account) => (
              <button
                key={account.id}
                onClick={() => handleAccountSelect(account.id)}
                className={`p-4 rounded-xl border-2 transition-all duration-200 text-left ${
                  selectedAccountIds.includes(account.id)
                    ? theme === 'dark'
                      ? 'border-blue-400 bg-blue-900/30 text-blue-400'
                      : 'border-blue-500 bg-blue-50 text-blue-600'
                    : theme === 'dark'
                      ? 'border-dark-600 bg-dark-800 text-white hover:border-blue-400'
                      : 'border-gray-200 bg-white text-gray-900 hover:border-blue-300'
                } hover:shadow-lg`}
              >
                <div className="flex items-center space-x-3">
                  {account.avatar ? (
                    <img
                      src={account.avatar}
                      alt={account.nickname}
                      className="w-12 h-12 rounded-full"
                    />
                  ) : (
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                      theme === 'dark' ? 'bg-dark-600' : 'bg-gray-300'
                    }`}>
                      <User className={`h-6 w-6 ${
                        theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                      }`} />
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{account.nickname}</div>
                    <div className={`text-xs truncate ${
                      theme === 'dark' ? 'text-dark-400' : 'text-gray-500'
                    }`}>
                      {new Date(account.addedAt).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="flex flex-col items-center space-y-1">
                    {selectedAccountIds.includes(account.id) ? (
                      <CheckCircle className="h-5 w-5 text-blue-400" />
                    ) : (
                      getAccountStatusIcon(account)
                    )}
                  </div>
                </div>
              </button>
            ))
          )}
        </div>

        {selectedAccountIds.length > 0 && (
          <div className={`p-3 rounded-lg ${
            theme === 'dark' ? 'bg-dark-800' : 'bg-gray-50'
          }`}>
            <div className="text-sm space-y-1">
              <div className={`font-medium ${
                theme === 'dark' ? 'text-white' : 'text-gray-900'
              }`}>
                当前选择：
              </div>
              {selectedAccountIds.map(accountId => {
                const account = accounts.find(acc => acc.id === accountId);
                return account ? (
                  <div key={accountId} className={`flex items-center space-x-2 ${
                    theme === 'dark' ? 'text-dark-300' : 'text-gray-600'
                  }`}>
                    <span>• {account.nickname}</span>
                    {getAccountStatusIcon(account)}
                  </div>
                ) : null;
              })}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default UserSelector;