import React from 'react';
import { Radar, Sun, Moon } from 'lucide-react';
import { useTheme } from '../hooks/useTheme';
import Button from './Button';

interface LayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  className?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  showHeader = true,
  className = '',
}) => {
  const { theme, toggleTheme } = useTheme();

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'dark'
        ? 'bg-gradient-to-br from-dark-900 via-dark-800 to-dark-900'
        : 'bg-gradient-to-br from-gray-50 via-white to-gray-100'
    } ${className}`}>
      {showHeader && (
        <header className={`relative z-10 backdrop-blur-md border-b transition-colors duration-300 ${
          theme === 'dark'
            ? 'bg-dark-900/50 border-dark-700'
            : 'bg-white/50 border-gray-200'
        }`}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-2">
                <div className="flex items-center justify-center w-8 h-8 bg-primary-600 rounded-lg">
                  <Radar className="h-5 w-5 text-white" />
                </div>
                <span className={`text-xl font-bold transition-colors duration-300 ${
                  theme === 'dark' ? 'text-white' : 'text-gray-900'
                }`}>ReachRadar</span>
              </div>

              <div className="flex items-center space-x-4">
                <nav className="hidden md:flex items-center space-x-6">
                  <a href="#features" className={`transition-colors duration-300 ${
                    theme === 'dark'
                      ? 'text-dark-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}>
                    功能特性
                  </a>
                  <a href="#about" className={`transition-colors duration-300 ${
                    theme === 'dark'
                      ? 'text-dark-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}>
                    关于我们
                  </a>
                  <a href="#contact" className={`transition-colors duration-300 ${
                    theme === 'dark'
                      ? 'text-dark-300 hover:text-white'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}>
                    联系我们
                  </a>
                </nav>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleTheme}
                  icon={theme === 'dark' ? Sun : Moon}
                  className="p-2"
                >
                  <span className="sr-only">切换主题</span>
                </Button>
              </div>
            </div>
          </div>
        </header>
      )}

      <main className="relative z-0">
        {children}
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full blur-3xl transition-opacity duration-300 ${
          theme === 'dark' ? 'bg-primary-500/10' : 'bg-primary-500/5'
        }`} />
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full blur-3xl transition-opacity duration-300 ${
          theme === 'dark' ? 'bg-blue-500/10' : 'bg-blue-500/5'
        }`} />
      </div>
    </div>
  );
};

export default Layout;
