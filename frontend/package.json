{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run obfuscate && tsc -b && vite build", "build:dev": "tsc -b && vite build", "obfuscate": "node scripts/obfuscate.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "reactflow": "^11.11.4"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "javascript-obfuscator": "^4.1.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0", "webpack-obfuscator": "^3.5.1"}}