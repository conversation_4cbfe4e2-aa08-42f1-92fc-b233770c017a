export default {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 1,
  numbersToExpressions: true,
  simplify: true,
  stringArrayShuffle: true,
  splitStrings: true,
  stringArray: true,
  stringArrayThreshold: 1,
  stringArrayIndexShift: true,
  stringArrayRotate: true,
  stringArrayWrappersCount: 5,
  stringArrayWrappersChainedCalls: true,
  stringArrayWrappersParametersMaxCount: 5,
  stringArrayWrappersType: 'function',
  stringArrayEncoding: ['rc4'],
  unicodeEscapeSequence: true,
  debugProtection: false,
  debugProtectionInterval: 0,
  disableConsoleOutput: false,
  domainLock: [],
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  
  // 标识符混淆
  identifierNamesGenerator: 'hexadecimal',
  identifiersPrefix: '',
  renameGlobals: false,
  renameProperties: false,
  
  // 自我保护
  selfDefending: true,
  
  // 源码映射
  sourceMap: false,
  sourceMapBaseUrl: '',
  sourceMapFileName: '',
  sourceMapMode: 'separate',
  
  // 转换对象键
  transformObjectKeys: true,
  
  // 目标环境
  target: 'browser'
};
