import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    host: '0.0.0.0',
    port: 5173,
    proxy: {
      '/api': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        secure: false,
      },
      '/health': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        secure: false,
      },
      '/docs': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        secure: false,
      },
      '/redoc': {
        target: 'http://localhost:8004',
        changeOrigin: true,
        secure: false,
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          ui: ['lucide-react'],
          query: ['@tanstack/react-query'],
          flow: ['reactflow']
        }
      }
    }
  }
})
