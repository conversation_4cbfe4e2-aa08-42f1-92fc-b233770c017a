import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import JavaScriptObfuscator from 'javascript-obfuscator';
import { createRequire } from 'module';
import obfuscatorConfig from '../obfuscator.config.js';

const require = createRequire(import.meta.url);
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const filesToObfuscate = [];

// TypeScript编译器
const ts = require('typescript');

function compileTypeScript(filePath) {
  const sourceCode = fs.readFileSync(filePath, 'utf8');
  const result = ts.transpile(sourceCode, {
    target: ts.ScriptTarget.ES2020,
    module: ts.ModuleKind.ES2020,
    moduleResolution: ts.ModuleResolutionKind.NodeJs,
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
    strict: true
  });
  return result;
}

function obfuscateFile(filePath) {
  try {
    console.log(`正在混淆文件: ${filePath}`);
    
    // 编译TypeScript到JavaScript
    const jsCode = compileTypeScript(filePath);
    
    // 混淆JavaScript代码
    const obfuscatedResult = JavaScriptObfuscator.obfuscate(jsCode, obfuscatorConfig);
    
    // 生成输出文件路径
    const outputPath = filePath.replace('.ts', '.obfuscated.js');
    
    // 写入混淆后的代码
    fs.writeFileSync(outputPath, obfuscatedResult.getObfuscatedCode());
    
    console.log(`✓ 混淆完成: ${outputPath}`);
    
    return outputPath;
  } catch (error) {
    console.error(`✗ 混淆失败 ${filePath}:`, error.message);
    return null;
  }
}

function main() {
  console.log('开始混淆文件...\n');

  if (filesToObfuscate.length === 0) {
    console.log('没有需要混淆的文件');
    return;
  }

  const obfuscatedFiles = [];

  for (const file of filesToObfuscate) {
    const fullPath = path.join(__dirname, '..', file);
    if (fs.existsSync(fullPath)) {
      const obfuscatedPath = obfuscateFile(fullPath);
      if (obfuscatedPath) {
        obfuscatedFiles.push(obfuscatedPath);
      }
    } else {
      console.log(`⚠ 文件不存在: ${file}`);
    }
  }

  console.log(`\n混淆完成! 共处理 ${obfuscatedFiles.length} 个文件`);
}

// 直接运行
main();
