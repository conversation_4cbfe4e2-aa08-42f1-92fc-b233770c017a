from typing import Dict, Any
from fastapi import APIRouter

from core.responses import error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger, log_error_with_context
from models.douyin import UserInfoRequest
from services.douyin import douyin_user_info_service

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/douyin/user",
    tags=["抖音用户信息"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.post("/info", response_model=Dict[str, Any])
async def get_user_info(request: UserInfoRequest) -> Dict[str, Any]:
    try:
        return douyin_user_info_service.get_user_info(request)
    except Exception as e:
        log_error_with_context(
            logger,
            "用户信息获取失败",
            error=e,
            context={"sec_uid": request.sec_uid}
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="用户信息获取失败，请稍后重试"
        )


