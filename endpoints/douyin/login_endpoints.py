
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, BackgroundTasks

from core.responses import success_response, error_response
from core.errors import <PERSON>rrorCodes, DouyinSessionException
from core.logging_config import get_logger, log_error_with_context
from models.douyin import (
    LoginStartRequest, LoginStartResponse, LoginStatusResponse,
    LoginConfirmResponse, CookieResponse, LoginStatus,
    CookieValidationRequest
)
from services.douyin import douyin_login_service

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/douyin/login",
    tags=["抖音登录"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.post("/start", response_model=Dict[str, Any])
async def start_login(
    request: LoginStartRequest,
    background_tasks: BackgroundTasks
) -> Dict[str, Any]:
    try:
        if douyin_login_service.get_active_sessions_count() >= 10:
            return error_response(
                code=ErrorCodes.RATE_LIMITED,
                message="当前登录会话过多，请稍后再试"
            )

        session = douyin_login_service.start_login_session(request)
        
        # 添加后台任务清理过期会话
        background_tasks.add_task(douyin_login_service.cleanup_expired_sessions)
        
        response_data = LoginStartResponse(
            session_id=session.session_id,
            status=session.status,
            expires_at=session.expires_at,
            message="登录会话已启动，浏览器正在打开中..."
        )
        
        return success_response(
            data=response_data.model_dump(),
            message="登录会话启动成功"
        )
        
    except Exception as e:
        log_error_with_context(
            logger,
            "启动登录会话失败",
            error=e,
            request_data=request.model_dump()
        )
        return error_response(
            code=ErrorCodes.DOUYIN_AUTH_FAILED,
            message="启动登录会话失败，请稍后重试"
        )


@router.get("/status/{session_id}", response_model=Dict[str, Any])
async def get_login_status(session_id: str) -> Dict[str, Any]:
    """
    查询登录会话状态
    
    - **session_id**: 会话ID
    """
    try:
        session = douyin_login_service.get_session_status(session_id)
        
        response_data = LoginStatusResponse(
            session_id=session.session_id,
            status=session.status,
            created_at=session.created_at,
            updated_at=session.updated_at,
            expires_at=session.expires_at,
            message=_get_status_message(session.status),
            error_message=session.error_message
        )
        
        return success_response(
            data=response_data.model_dump(),
            message="状态查询成功"
        )

    except DouyinSessionException as e:
        return error_response(
            code=e.error_code,
            message=e.message
        )
    except Exception as e:
        log_error_with_context(
            logger,
            "查询登录状态失败",
            error=e,
            session_id=session_id
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="查询登录状态失败，请稍后重试"
        )


@router.post("/confirm/{session_id}", response_model=Dict[str, Any])
async def confirm_login(session_id: str) -> Dict[str, Any]:
    """
    确认登录并抓取Cookie
    
    - **session_id**: 会话ID
    """
    try:
        session = douyin_login_service.get_session_status(session_id)
        
        if not session:
            return error_response(
                code=ErrorCodes.NOT_FOUND,
                message="登录会话不存在或已过期"
            )
        
        if session.status not in [LoginStatus.READY, LoginStatus.SCANNING]:
            return error_response(
                code=ErrorCodes.DOUYIN_AUTH_FAILED,
                message=f"当前会话状态不允许确认登录: {session.status}"
            )
        
        # 确认登录并抓取Cookie
        success = douyin_login_service.confirm_login(session_id)
        
        if success:
            updated_session = douyin_login_service.get_session_status(session_id)
            response_data = LoginConfirmResponse(
                session_id=session_id,
                status=updated_session.status,
                cookies_count=len(updated_session.cookies),
                message="登录确认成功，Cookie已抓取"
            )
            
            return success_response(
                data=response_data.model_dump(),
                message="登录确认成功"
            )
        else:
            return error_response(
                code=ErrorCodes.DOUYIN_AUTH_FAILED,
                message="登录确认失败，请确保已完成扫码登录"
            )
            
    except Exception as e:
        logger.error(f"确认登录失败: {str(e)}")
        return error_response(
            code=ErrorCodes.DOUYIN_AUTH_FAILED,
            message=f"确认登录失败: {str(e)}"
        )


@router.get("/cookies/{session_id}", response_model=Dict[str, Any])
async def get_cookies(session_id: str) -> Dict[str, Any]:
    """
    获取登录会话的Cookie
    
    - **session_id**: 会话ID
    """
    try:
        session = douyin_login_service.get_session_status(session_id)
        
        if not session:
            return error_response(
                code=ErrorCodes.NOT_FOUND,
                message="登录会话不存在或已过期"
            )
        
        if session.status != LoginStatus.SUCCESS:
            return error_response(
                code=ErrorCodes.DOUYIN_AUTH_FAILED,
                message="会话尚未成功登录，无法获取Cookie"
            )
        
        cookies = douyin_login_service.get_session_cookies(session_id)
        
        response_data = CookieResponse(
            session_id=session_id,
            cookies=cookies,
            total_count=len(cookies),
            extracted_at=session.updated_at
        )
        
        return success_response(
            data=response_data.model_dump(),
            message="Cookie获取成功"
        )
        
    except Exception as e:
        log_error_with_context(
            logger,
            "获取Cookie失败",
            error=e,
            session_id=session_id
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="获取Cookie失败，请稍后重试"
        )


@router.delete("/cancel/{session_id}", response_model=Dict[str, Any])
async def cancel_login(session_id: str) -> Dict[str, Any]:
    """
    取消登录会话
    
    - **session_id**: 会话ID
    """
    try:
        success = douyin_login_service.cancel_session(session_id)
        
        if success:
            return success_response(
                data={"session_id": session_id},
                message="登录会话已取消"
            )
        else:
            return error_response(
                code=ErrorCodes.NOT_FOUND,
                message="登录会话不存在或取消失败"
            )
            
    except Exception as e:
        log_error_with_context(
            logger,
            "取消登录会话失败",
            error=e,
            session_id=session_id
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="取消登录会话失败，请稍后重试"
        )


@router.get("/sessions", response_model=Dict[str, Any])
async def get_active_sessions() -> Dict[str, Any]:
    """
    获取当前活跃的登录会话信息（管理接口）
    """
    try:
        count = douyin_login_service.get_active_sessions_count()
        
        return success_response(
            data={
                "active_sessions_count": count,
                "max_sessions": 10,
                "timestamp": datetime.now().isoformat()
            },
            message="活跃会话信息获取成功"
        )
        
    except Exception as e:
        log_error_with_context(
            logger,
            "获取活跃会话信息失败",
            error=e
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="获取活跃会话信息失败，请稍后重试"
        )



@router.post("/validate", response_model=Dict[str, Any])
async def validate_cookies(request: CookieValidationRequest) -> Dict[str, Any]:
    """
    验证Cookie的有效性

    通过调用抖音API接口验证Cookie是否有效：
    - **cookies**: Cookie字符串，格式：name1=value1; name2=value2
    - **timeout_seconds**: 验证超时时间（秒），默认10秒

    验证原理：调用 https://www.douyin.com/aweme/v1/web/aweme/post/?aid=6383
    如果能成功获取数据，说明Cookie有效
    """
    try:
        logger.info("开始验证Cookie有效性")

        # 调用服务验证Cookie
        validation_result = douyin_login_service.validate_cookies(request)

        if validation_result.is_valid:
            return success_response(
                data=validation_result.model_dump(),
                message="Cookie验证成功，身份校验通过"
            )
        else:
            return error_response(
                code=ErrorCodes.DOUYIN_AUTH_FAILED,
                message="身份校验失败，请重新登录",
                data={
                    "is_valid": False,
                    "status_code": validation_result.status_code,
                    "validation_time": validation_result.validation_time.isoformat()
                }
            )

    except Exception as e:
        log_error_with_context(
            logger,
            "Cookie验证失败",
            error=e
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="Cookie验证失败，请稍后重试"
        )



def _get_status_message(status: LoginStatus) -> str:
    """根据状态获取状态消息"""
    status_messages = {
        LoginStatus.PENDING: "正在启动浏览器...",
        LoginStatus.READY: "浏览器已就绪，请在打开的页面中扫码登录",
        LoginStatus.SCANNING: "检测到扫码操作，请在手机上确认登录",
        LoginStatus.SUCCESS: "登录成功，Cookie已抓取",
        LoginStatus.FAILED: "登录失败",
        LoginStatus.EXPIRED: "会话已过期",
        LoginStatus.CANCELLED: "会话已取消"
    }
    return status_messages.get(status, "未知状态")
