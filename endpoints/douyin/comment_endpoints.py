from typing import Dict, Any
from fastapi import APIRouter

from core.responses import success_response, error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger, log_error_with_context
from models.douyin import CommentRequest
from services.douyin import douyin_comment_service

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/douyin/comment",
    tags=["抖音评论"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.post("/get", response_model=Dict[str, Any])
async def get_comments(request: CommentRequest) -> Dict[str, Any]:
    try:
        result = douyin_comment_service.get_comments(request)

        if isinstance(result, dict) and 'success' in result:
            return result

        return success_response(
            data=result,
            message="评论获取成功"
        )

    except Exception as e:
        log_error_with_context(
            logger,
            "获取评论失败",
            error=e,
            context={"aweme_id": request.aweme_id}
        )
        return error_response(
            code=ErrorCodes.DOUYIN_API_ERROR,
            message="获取评论失败，请稍后重试"
        )
