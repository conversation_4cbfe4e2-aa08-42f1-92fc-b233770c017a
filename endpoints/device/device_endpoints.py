from typing import Dict, Any

from fastapi import APIRouter

from core.responses import success_response, error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger, log_error_with_context
from models.device import DeviceVerificationRequest
from services.device import device_service

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/device",
    tags=["设备验证"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.post("/verify", response_model=Dict[str, Any])
async def verify_device(request: DeviceVerificationRequest) -> Dict[str, Any]:
    try:
        verification_result = device_service.verify_device(request)

        if not verification_result.device_fingerprint:
            return error_response(
                code=ErrorCodes.DEVICE_FINGERPRINT_FAILED,
                message="设备指纹生成失败"
            )

        return success_response(
            data=verification_result.model_dump(),
            message=verification_result.message
        )

    except Exception as e:
        log_error_with_context(logger, "设备验证失败", error=e)
        return error_response(
            code=ErrorCodes.DEVICE_VERIFICATION_ERROR,
            message="设备验证服务异常"
        )


@router.get("/info", response_model=Dict[str, Any])
async def get_device_info() -> Dict[str, Any]:
    try:
        enabled_devices = device_service.get_developer_devices_count()

        info_data = {
            "verification_enabled": enabled_devices > 0,
            "status": "已配置开发者设备验证" if enabled_devices > 0 else "未配置开发者设备"
        }

        return success_response(data=info_data, message="获取成功")

    except Exception as e:
        log_error_with_context(logger, "获取设备信息失败", error=e)
        return error_response(
            code=ErrorCodes.DEVICE_VERIFICATION_ERROR,
            message="获取设备信息失败"
        )



