from typing import Dict, Any
from fastapi import APIRouter, Query
from fastapi.responses import RedirectResponse

from core.responses import success_response, error_response
from core.errors import ErrorCodes, DouyinServiceException
from core.logging_config import get_logger, log_error_with_context
from models.douyin_official import (
    OAAuthRequest, OACallbackRequest, OATokenRefreshRequest, OATokenValidateRequest
)
from services.douyin_official import douyin_oa_service

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/douyin-official/oa",
    tags=["抖音官方OA"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)

oauth_router = APIRouter(
    prefix="/api/douyin",
    tags=["抖音OAuth回调"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)


@router.post("/auth", response_model=Dict[str, Any])
async def get_auth_url(request: OAAuthRequest) -> Dict[str, Any]:
    try:
        auth_response = douyin_oa_service.generate_auth_url(request)
        return success_response(
            data=auth_response.model_dump(),
            message="授权URL生成成功"
        )
    except DouyinServiceException as e:
        log_error_with_context(logger, "生成授权URL失败", error=e)
        return error_response(code=e.error_code, message="授权服务暂时不可用，请稍后重试")
    except Exception as e:
        log_error_with_context(logger, "生成授权URL异常", error=e)
        return error_response(
            code=ErrorCodes.DOUYIN_OA_AUTH_FAILED,
            message="生成授权URL失败，请稍后重试"
        )



@router.post("/token/refresh", response_model=Dict[str, Any])
async def refresh_access_token(request: OATokenRefreshRequest) -> Dict[str, Any]:
    try:
        token_info = douyin_oa_service.refresh_token(request)
        return success_response(
            data=token_info.model_dump(),
            message="访问令牌刷新成功"
        )
    except DouyinServiceException as e:
        log_error_with_context(logger, "刷新访问令牌失败", error=e)
        return error_response(code=e.error_code, message="令牌刷新失败，请重新授权")
    except Exception as e:
        log_error_with_context(logger, "刷新访问令牌异常", error=e)
        return error_response(
            code=ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED,
            message="刷新访问令牌失败，请稍后重试"
        )


@router.get("/user/info", response_model=Dict[str, Any])
async def get_user_info(
    access_token: str = Query(...),
    open_id: str = Query(...)
) -> Dict[str, Any]:
    try:
        user_info = douyin_oa_service.get_user_info(access_token, open_id)
        return success_response(
            data=user_info.model_dump(),
            message="用户信息获取成功"
        )
    except DouyinServiceException as e:
        log_error_with_context(logger, "获取用户信息失败", error=e)
        return error_response(code=e.error_code, message="用户信息获取失败，请检查授权状态")
    except Exception as e:
        log_error_with_context(logger, "获取用户信息异常", error=e)
        return error_response(
            code=ErrorCodes.DOUYIN_OA_AUTH_FAILED,
            message="获取用户信息失败，请稍后重试"
        )


@oauth_router.get("/oauth/callback")
async def douyin_oauth_callback(
    code: str = Query(...),
    state: str = Query(...),
    scopes: str = Query(...)
) -> RedirectResponse:

    try:
        # 直接处理令牌交换
        callback_request = OACallbackRequest(code=code, state=state)
        token_info = douyin_oa_service.handle_callback(callback_request)

        # 获取用户信息
        user_info = douyin_oa_service.get_user_info(token_info.access_token, token_info.open_id)

        # 保存到数据库
        douyin_oa_service.save_account_to_database(token_info, user_info)

        # 生成包含完整信息的前端重定向URL
        frontend_url = douyin_oa_service.get_oauth_success_url(token_info, user_info, scopes)
        return RedirectResponse(url=frontend_url)

    except DouyinServiceException as e:
        log_error_with_context(logger, "OAuth回调处理异常", error=e)
        error_url = douyin_oa_service.get_oauth_error_url(str(e))
        return RedirectResponse(url=error_url)
    except Exception as e:
        log_error_with_context(logger, "OAuth回调异常", error=e)
        error_url = douyin_oa_service.get_oauth_error_url("callback_failed")
        return RedirectResponse(url=error_url)


@router.post("/token/validate", response_model=Dict[str, Any])
async def validate_token(request: OATokenValidateRequest) -> Dict[str, Any]:
    try:
        validation_result = douyin_oa_service.validate_token(request)

        if validation_result.is_valid:
            return success_response(
                data=validation_result.model_dump(),
                message="令牌验证成功"
            )
        else:
            return error_response(
                code=ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED,
                message="令牌已失效，请重新授权",
                data=validation_result.model_dump()
            )

    except Exception as e:
        log_error_with_context(logger, "验证令牌异常", error=e)
        return error_response(
            code=ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED,
            message="令牌验证服务暂时不可用，请稍后重试"
        )


@router.get("/account/{open_id}", response_model=Dict[str, Any])
async def get_account_info(open_id: str) -> Dict[str, Any]:

    try:
        account_info = douyin_oa_service.get_account_info(open_id)
        return success_response(
            data=account_info,
            message="账号信息获取成功"
        )

    except DouyinServiceException as e:
        log_error_with_context(logger, "获取账号信息失败", error=e)
        return error_response(code=e.error_code, message="获取账号信息失败，请稍后重试")
    except Exception as e:
        log_error_with_context(logger, "获取账号信息异常", error=e)
        return error_response(
            code=ErrorCodes.DOUYIN_OA_AUTH_FAILED,
            message="获取账号信息失败，请稍后重试"
        )
