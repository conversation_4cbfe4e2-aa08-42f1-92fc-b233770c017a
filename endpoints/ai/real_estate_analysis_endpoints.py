from fastapi import APIRouter
from typing import Dict, Any

from core.responses import error_response
from core.errors import ErrorCodes
from config.settings import settings
from models.ai import RealEstateAnalysisRequest
from services.ai import real_estate_analysis_service

router = APIRouter(
    prefix="/api/ai/real-estate",
    tags=["AI房地产分析"]
)


@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_videos(request: RealEstateAnalysisRequest) -> Dict[str, Any]:
    try:
        if request.admin_password != settings.LIANDONG_ADMIN_PASSWORD:
            return error_response(
                code=ErrorCodes.UNAUTHORIZED,
                message="管理员密码错误，无权限执行此操作"
            )

        if request.timeout_seconds < 10 or request.timeout_seconds > 300:
            return error_response(
                code=ErrorCodes.AI_INVALID_INPUT,
                message="超时时间必须在10-300秒之间"
            )

        return real_estate_analysis_service.analyze_videos(request)

    except Exception:
        return error_response(
            code=ErrorCodes.AI_SERVICE_ERROR,
            message="分析服务异常"
        )