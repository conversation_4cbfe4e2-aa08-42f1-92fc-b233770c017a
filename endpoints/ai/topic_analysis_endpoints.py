from fastapi import APIRouter
from typing import Dict, Any

from core.responses import error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger
from models.ai import TopicAnalysisRequest
from services.ai import topic_analysis_service

router = APIRouter(
    prefix="/api/ai/topic",
    tags=["AI话题分析"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)

logger = get_logger(__name__)


@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_topic(request: TopicAnalysisRequest) -> Dict[str, Any]:
    try:
        if not request.text or not request.text.strip():
            return error_response(
                code=ErrorCodes.AI_INVALID_INPUT,
                message="分析文本不能为空"
            )

        if len(request.text.strip()) > 1000:
            return error_response(
                code=ErrorCodes.AI_INVALID_INPUT,
                message="分析文本过长，请控制在1000字符以内"
            )

        return topic_analysis_service.analyze_topic(request)

    except Exception as e:
        logger.error(f"话题分析端点异常: {str(e)}")
        return error_response(
            code=ErrorCodes.AI_SERVICE_ERROR,
            message="话题分析服务异常，请稍后重试"
        )
