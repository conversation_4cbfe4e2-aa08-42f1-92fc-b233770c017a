from fastapi import APIRouter
from typing import Dict, Any

from core.responses import error_response
from core.errors import ErrorCodes
from core.logging_config import get_logger
from models.ai import CommentCustomerAnalysisRequest
from services.ai import customer_analysis_service

router = APIRouter(
    prefix="/api/ai/customer",
    tags=["AI客户分析"],
    responses={
        ErrorCodes.NOT_FOUND_HTTP: {"description": "未找到"},
        ErrorCodes.INTERNAL_SERVER_ERROR: {"description": "服务器内部错误"}
    }
)

logger = get_logger(__name__)


@router.post("/analyze", response_model=Dict[str, Any])
async def analyze_customer(request: CommentCustomerAnalysisRequest) -> Dict[str, Any]:
    """
    分析评论中的潜在客户
    
    根据comment.md中的提示词，分析评论内容，识别潜在的厂房租赁/购买客户
    """
    try:
        if not request.comments:
            return error_response(
                code=ErrorCodes.AI_INVALID_INPUT,
                message="评论列表不能为空"
            )

        if len(request.comments) > 3000:
            return error_response(
                code=ErrorCodes.AI_INVALID_INPUT,
                message="评论数量过多，请控制在3000条以内"
            )

        return customer_analysis_service.analyze_customer(request)

    except Exception as e:
        logger.error(f"客户分析端点异常: {str(e)}")
        return error_response(
            code=ErrorCodes.AI_SERVICE_ERROR,
            message="客户分析服务异常，请稍后重试"
        )
