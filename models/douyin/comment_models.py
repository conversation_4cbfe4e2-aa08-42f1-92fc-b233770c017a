from pydantic import BaseModel, Field, field_validator, model_validator
from typing import List, Union, Optional, Dict, Any
from .login_models import CookieData


class VideoFilterInfo(BaseModel):
    """视频过滤信息"""
    aweme_id: str = Field(..., description="视频ID")
    create_time: Optional[int] = Field(None, description="视频发布时间戳")
    comment_count: Optional[int] = Field(None, description="评论数量")


class CommentRequest(BaseModel):
    aweme_id: Optional[str] = Field(None, description="抖音视频ID（单视频模式）")
    cursor: str = Field(default="0", description="分页游标，默认从0开始")
    cookies: Union[List[CookieData], str] = Field(..., description="用户Cookie数据，支持对象数组或字符串格式")
    timeout_seconds: int = Field(default=30, description="请求超时时间（秒）")

    # 完整评论获取字段
    fetch_all_comments: bool = Field(default=False, description="是否获取完整评论，默认只获取第一页")
    max_comments_limit: int = Field(default=500, description="最大评论获取数量限制")

    # 批量模式字段
    video_list: Optional[List[VideoFilterInfo]] = Field(None, description="视频列表（批量模式）")
    max_months_old: Optional[int] = Field(default=5, description="最大月数，超过则跳过")
    min_comment_count: Optional[int] = Field(default=1, description="最小评论数，低于则跳过")

    @field_validator('cookies')
    @classmethod
    def validate_cookies(cls, v):
        """验证并转换cookies格式"""
        if isinstance(v, str):
            if not v.strip():
                raise ValueError("Cookie字符串不能为空")

            cookies = []
            for cookie_pair in v.split(';'):
                cookie_pair = cookie_pair.strip()
                if '=' in cookie_pair:
                    name, value = cookie_pair.split('=', 1)
                    cookies.append(CookieData(
                        name=name.strip(),
                        value=value.strip(),
                        domain='.douyin.com',
                        path='/'
                    ))
            return cookies
        elif isinstance(v, list):
            if not v:
                raise ValueError("Cookie列表不能为空")
            return v
        else:
            raise ValueError("Cookies必须是字符串或CookieData对象列表")

    @model_validator(mode='after')
    def validate_request_mode(self):
        """验证请求模式"""
        if not self.aweme_id and not self.video_list:
            raise ValueError("必须提供aweme_id或video_list中的至少一个")
        if self.aweme_id and self.video_list:
            raise ValueError("aweme_id和video_list不能同时提供，请选择单视频模式或批量模式")
        return self


class CommentInfo(BaseModel):
    cid: str = Field(..., description="评论ID")
    aweme_id: str = Field(..., description="视频ID")
    text: str = Field(..., description="评论内容")
    user_nickname: str = Field(..., description="用户昵称")
    user_uid: str = Field(..., description="用户UID")
    user_short_id: str = Field(default="", description="用户抖音号")
    user_sec_uid: str = Field(default="", description="用户安全UID，用于获取用户详细信息")
    user_avatar: str = Field(default="", description="用户头像URL")
    create_time: int = Field(..., description="评论创建时间戳")
    digg_count: int = Field(default=0, description="点赞数")
    reply_count: int = Field(default=0, description="回复数")
    is_author: bool = Field(default=False, description="是否为作者评论")
    ip_label: str = Field(default="", description="评论者IP地址标签")


class CommentResult(BaseModel):
    comments: List[CommentInfo] = Field(default_factory=list, description="评论列表")
    has_more: bool = Field(default=False, description="是否还有更多评论")
    cursor: str = Field(default="0", description="下一页游标")
    total: int = Field(default=0, description="评论总数")
    message: str = Field(default="", description="结果消息")

    # 完整评论获取统计字段
    fetched_count: int = Field(default=0, description="实际获取的评论数量")
    is_limited: bool = Field(default=False, description="是否达到获取数量限制")
    pages_fetched: int = Field(default=1, description="获取的页数")

    # 批量模式字段
    processed_videos: List[str] = Field(default_factory=list, description="已处理的视频ID列表")
    skipped_videos: List[Dict[str, Any]] = Field(default_factory=list, description="跳过的视频及原因")
    filter_stats: Dict[str, int] = Field(default_factory=dict, description="过滤统计信息")
