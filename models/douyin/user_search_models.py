from typing import Optional, List, Union
from pydantic import BaseModel, Field, field_validator
from .login_models import CookieData


class UserInfoRequest(BaseModel):
    sec_uid: Union[str, List[str]] = Field(..., description="用户安全UID，支持单个或批量查询")
    cookies: Union[List[CookieData], str] = Field(..., description="用户Cookie数据")
    timeout_seconds: Optional[int] = Field(default=10, ge=1, le=30, description="请求超时时间")

    @field_validator('sec_uid')
    @classmethod
    def validate_sec_uid(cls, v):
        if isinstance(v, str):
            if not v.strip():
                raise ValueError("sec_uid不能为空")
            return v.strip()
        elif isinstance(v, list):
            if not v:
                raise ValueError("sec_uid列表不能为空")
            if len(v) > 10:
                raise ValueError("sec_uid列表最多只能包含10个元素")
            return [uid.strip() for uid in v if uid.strip()]
        else:
            raise ValueError("sec_uid必须是字符串或字符串列表")

    @field_validator('cookies')
    @classmethod
    def validate_cookies(cls, v):
        if isinstance(v, str):
            if not v.strip():
                raise ValueError("Cookie字符串不能为空")
            cookies = []
            for cookie_pair in v.split(';'):
                cookie_pair = cookie_pair.strip()
                if '=' in cookie_pair:
                    name, value = cookie_pair.split('=', 1)
                    cookies.append(CookieData(
                        name=name.strip(),
                        value=value.strip(),
                        domain='.douyin.com',
                        path='/'
                    ))
            return cookies
        elif isinstance(v, list):
            if not v:
                raise ValueError("Cookie列表不能为空")
            return v
        else:
            raise ValueError("Cookies必须是字符串或CookieData对象列表")


class DouyinUserInfo(BaseModel):
    short_id: str = Field(..., description="抖音号")
    nickname: str = Field(..., description="用户昵称")
    signature: str = Field(..., description="个性签名")
    uid: Optional[str] = Field(None, description="用户ID")
    sec_uid: Optional[str] = Field(None, description="用户安全UID")
    avatar_url: Optional[str] = Field(None, description="头像URL")


class UserInfoResult(BaseModel):
    users: List[DouyinUserInfo] = Field(default_factory=list, description="用户信息列表")
    total_requested: int = Field(..., description="请求的用户数量")
    total_found: int = Field(..., description="找到的用户数量")
    message: str = Field(..., description="获取结果说明")



