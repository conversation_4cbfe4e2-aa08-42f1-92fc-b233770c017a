from datetime import datetime
from enum import Enum
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, field_validator
import uuid


class LoginStatus(str, Enum):
    PENDING = "pending"
    READY = "ready"
    SCANNING = "scanning"
    SUCCESS = "success"
    FAILED = "failed"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


class CookieData(BaseModel):
    name: str
    value: str
    domain: str
    path: str = "/"
    expires: Optional[datetime] = None
    secure: bool = False
    http_only: bool = False
    same_site: Optional[str] = None


class LoginSession(BaseModel):
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    status: LoginStatus = LoginStatus.PENDING
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    expires_at: datetime
    browser_pid: Optional[int] = None
    cookies: List[CookieData] = Field(default_factory=list)
    error_message: Optional[str] = None
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class LoginStartRequest(BaseModel):
    timeout_minutes: Optional[int] = Field(default=10, ge=1, le=60)
    headless: Optional[bool] = False


class LoginStartResponse(BaseModel):
    session_id: str
    status: LoginStatus
    expires_at: datetime
    message: str


class LoginStatusResponse(BaseModel):
    session_id: str
    status: LoginStatus
    created_at: datetime
    updated_at: datetime
    expires_at: datetime
    message: str
    error_message: Optional[str] = None


class LoginConfirmResponse(BaseModel):
    session_id: str
    status: LoginStatus
    cookies_count: int
    message: str


class CookieResponse(BaseModel):
    session_id: str
    cookies: List[CookieData]
    total_count: int
    extracted_at: datetime


class CookieValidationRequest(BaseModel):
    cookies: Union[List[CookieData], str]
    timeout_seconds: Optional[int] = Field(default=10, ge=5, le=30)

    @field_validator('cookies')
    @classmethod
    def validate_cookies(cls, v):
        """验证并转换cookies格式"""
        if isinstance(v, str):
            # 如果是字符串格式，转换为CookieData对象数组
            if not v.strip():
                raise ValueError("Cookie字符串不能为空")

            cookie_list = []
            for cookie_str in v.split(';'):
                cookie_str = cookie_str.strip()
                if '=' in cookie_str:
                    name, value = cookie_str.split('=', 1)
                    cookie_list.append(CookieData(
                        name=name.strip(),
                        value=value.strip(),
                        domain="",
                        path="/",
                        secure=False,
                        http_only=False
                    ))
            return cookie_list
        elif isinstance(v, list):
            # 如果是列表，直接返回
            return v
        else:
            raise ValueError("Cookies必须是字符串或CookieData对象数组")


class CookieValidationResponse(BaseModel):
    is_valid: bool
    user_data: Optional[Dict[str, Any]] = None
    validation_time: datetime = Field(default_factory=datetime.now)
    error_message: Optional[str] = None
    status_code: Optional[int] = None

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
