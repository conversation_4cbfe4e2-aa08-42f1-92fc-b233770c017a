from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field


class SearchRequest(BaseModel):
    keyword: str = Field(..., min_length=1, max_length=100, description="搜索关键词")
    count: Optional[int] = Field(default=10, ge=1, le=50, description="单页返回结果数量（兼容模式）")
    offset: Optional[int] = Field(default=0, ge=0, description="偏移量（兼容模式）")
    pages: Optional[int] = Field(default=0, ge=0, le=1000, description="获取页数（批量模式，0表示第一页）")
    per_page: Optional[int] = Field(default=10, ge=1, le=50, description="每页数量（批量模式）")
    timeout_seconds: Optional[int] = Field(default=10, ge=5, le=7200, description="请求超时时间")


class VideoInfo(BaseModel):
    aweme_id: str = Field(..., description="视频ID")
    desc: str = Field(default="", description="视频描述")
    author_nickname: str = Field(default="", description="作者昵称")
    author_unique_id: str = Field(default="", description="作者唯一ID")
    create_time: Optional[int] = Field(default=None, description="创建时间戳")
    statistics: Optional[Dict[str, Any]] = Field(default=None, description="统计数据")
    douyin_link: str = Field(default="", description="抖音视频页面链接")


class SearchResult(BaseModel):
    videos: List[VideoInfo] = Field(default_factory=list, description="视频列表")
    total_count: int = Field(default=0, description="总数量")
    has_more: bool = Field(default=False, description="是否有更多")
    cursor: Optional[Union[str, int]] = Field(default=None, description="游标")


class SearchResponse(BaseModel):
    success: bool = Field(..., description="搜索是否成功")
    keyword: str = Field(..., description="搜索关键词")
    result: Optional[SearchResult] = Field(default=None, description="搜索结果")
    search_time: datetime = Field(default_factory=datetime.now, description="搜索时间")

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
