from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class DeviceInfo(BaseModel):
    """设备硬件信息模型"""
    # CPU信息
    cpu_cores: Optional[int] = Field(None, description="CPU核心数")
    cpu_architecture: Optional[str] = Field(None, description="CPU架构")
    
    # 内存信息
    memory_size: Optional[float] = Field(None, description="内存大小(GB)")
    
    # GPU信息
    gpu_vendor: Optional[str] = Field(None, description="GPU厂商")
    gpu_renderer: Optional[str] = Field(None, description="GPU渲染器")
    
    # 屏幕信息
    screen_width: Optional[int] = Field(None, description="屏幕宽度")
    screen_height: Optional[int] = Field(None, description="屏幕高度")
    screen_color_depth: Optional[int] = Field(None, description="屏幕色深")
    pixel_ratio: Optional[float] = Field(None, description="像素比")
    
    # 浏览器信息
    user_agent: Optional[str] = Field(None, description="用户代理")
    platform: Optional[str] = Field(None, description="操作系统平台")
    language: Optional[str] = Field(None, description="浏览器语言")
    timezone: Optional[str] = Field(None, description="时区")

    # Canvas指纹
    canvas_fingerprint: Optional[str] = Field(None, description="Canvas指纹")
    webgl_fingerprint: Optional[str] = Field(None, description="WebGL指纹")

    # 其他信息
    touch_support: Optional[bool] = Field(None, description="是否支持触摸")
    cookie_enabled: Optional[bool] = Field(None, description="是否启用Cookie")

    # 收集时间
    collected_at: datetime = Field(default_factory=datetime.now, description="信息收集时间")


class DeviceFingerprint(BaseModel):
    """设备指纹模型"""
    fingerprint: str = Field(..., description="设备指纹哈希值")
    device_info: DeviceInfo = Field(..., description="设备信息")
    generated_at: datetime = Field(default_factory=datetime.now, description="指纹生成时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DeviceVerificationRequest(BaseModel):
    """设备验证请求模型"""
    encrypted_device_info: str = Field(..., description="加密的设备信息")


class DeviceVerificationResponse(BaseModel):
    """设备验证响应模型"""
    is_developer: bool = Field(..., description="是否为开发者设备")
    device_fingerprint: str = Field(..., description="设备指纹")
    verification_time: datetime = Field(default_factory=datetime.now, description="验证时间")
    message: str = Field(..., description="验证结果消息")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class DeveloperDeviceConfig(BaseModel):
    """开发者设备配置模型"""
    device_name: str = Field(..., description="设备名称")
    device_fingerprint: str = Field(..., description="设备指纹")
    description: Optional[str] = Field(None, description="设备描述")
    enabled: bool = Field(True, description="是否启用")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    last_used: Optional[datetime] = Field(None, description="最后使用时间")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
