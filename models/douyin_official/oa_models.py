from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl
from enum import Enum


class OAScope(str, Enum):
    USER_INFO = "user_info"
    VIDEO_LIST = "video.list"
    VIDEO_DATA = "video.data"
    FANS_DATA = "fans.data"


class OAAuthRequest(BaseModel):
    scope: Optional[List[str]] = Field(default=None)


class OAAuthUrlResponse(BaseModel):
    auth_url: HttpUrl
    state: str
    expires_in: int = 600
    created_at: datetime = Field(default_factory=datetime.now)


class OACallbackRequest(BaseModel):
    code: str
    state: Optional[str] = None


class OATokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    expires_in: int
    refresh_expires_in: int
    scope: List[str]
    open_id: str
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OATokenInfo(BaseModel):
    access_token: str
    refresh_token: str
    expires_in: int
    refresh_expires_in: int
    scope: List[str]
    open_id: str
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class OAUserInfoResponse(BaseModel):
    open_id: str
    union_id: Optional[str] = None
    nickname: str
    avatar: Optional[HttpUrl] = None
    gender: Optional[int] = None
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None


class OAUserInfo(BaseModel):
    open_id: str
    union_id: Optional[str] = None
    nickname: str
    avatar: Optional[HttpUrl] = None
    gender: Optional[int] = None
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None


class OATokenRefreshRequest(BaseModel):
    refresh_token: str
    client_key: str


class OATokenValidateRequest(BaseModel):
    access_token: str
    open_id: str


class OATokenValidateResponse(BaseModel):
    is_valid: bool
    expires_in: Optional[int] = None
    scope: Optional[List[str]] = None
    error_message: Optional[str] = None
    validated_at: datetime = Field(default_factory=datetime.now)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
