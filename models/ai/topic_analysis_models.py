from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class TopicAnalysisRequest(BaseModel):
    """话题分析请求模型"""
    text: str = Field(..., description="需要分析的文本内容", min_length=1, max_length=1000)
    analysis_type: str = Field(default="general", description="分析类型：general(通用), person_info(人物信息), service(服务需求), learning(学习需求)等")
    timeout_seconds: int = Field(default=30, description="请求超时时间（秒）", ge=5, le=60)


class TopicResult(BaseModel):
    """话题分析结果"""
    main_topic: str = Field(..., description="提取的主要话题")
    sub_topics: list[str] = Field(default_factory=list, description="子话题列表")
    keywords: list[str] = Field(default_factory=list, description="关键词列表")
    entities: Dict[str, Any] = Field(default_factory=dict, description="实体信息（如地点、人名、组织等）")
    intent: Optional[str] = Field(None, description="用户意图（如求购、出售、咨询等）")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    category: Optional[str] = Field(None, description="话题分类")


class TopicAnalysisResponse(BaseModel):
    """话题分析响应模型"""
    original_text: str = Field(..., description="原始输入文本")
    result: TopicResult = Field(..., description="分析结果")
    processing_time: float = Field(..., description="处理时间（秒）")
    model_used: str = Field(..., description="使用的AI模型")

    model_config = {"protected_namespaces": ()}
