from typing import Optional, List, Dict
from pydantic import BaseModel, Field

# 导入已存在的VideoInfo模型
from models.douyin import VideoInfo


class RealEstateAnalysisRequest(BaseModel):
    admin_password: str
    videos: List[VideoInfo] = Field(..., description="要分析的视频列表")
    timeout_seconds: int = Field(default=60, ge=10, le=300)


class RealEstateAnalysisResult(BaseModel):
    aweme_id: str
    video_description: str
    is_real_estate_related: bool
    confidence_score: float = Field(ge=0.0, le=1.0)
    analysis_reasoning: str
    keywords_found: List[str] = Field(default_factory=list)
    category: Optional[str] = None
    intent_type: Optional[str] = None
    # 评论过滤所需字段
    create_time: Optional[int] = Field(default=None, description="视频发布时间戳")
    comment_count: Optional[int] = Field(default=None, description="视频评论数量")


class BatchAnalysisResponse(BaseModel):
    """统一的分析响应 - 支持单个或多个视频"""
    total_analyzed: int
    real_estate_count: int
    non_real_estate_count: int
    results: List[RealEstateAnalysisResult]
    processing_time: float
    success_rate: float = Field(ge=0.0, le=1.0)


class AnalysisStatsRequest(BaseModel):
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    category_filter: Optional[str] = None


class AnalysisStatsResponse(BaseModel):
    total_videos: int
    analyzed_videos: int
    real_estate_videos: int
    real_estate_percentage: float
    category_distribution: Dict[str, int] = Field(default_factory=dict)
    intent_distribution: Dict[str, int] = Field(default_factory=dict)
    average_confidence: float
    analysis_date_range: Dict[str, str] = Field(default_factory=dict)
