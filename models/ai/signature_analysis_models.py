from typing import List, Optional
from pydantic import BaseModel, Field


class SignatureAnalysisResult(BaseModel):
    signature: str = Field(..., description="原始个性签名")
    is_seller: bool = Field(..., description="是否为销售/卖家")
    confidence: float = Field(..., description="置信度", ge=0.0, le=1.0)
    reasoning: str = Field(..., description="判断理由")
    seller_indicators: List[str] = Field(default_factory=list, description="销售/卖家指标")
    category: Optional[str] = Field(None, description="销售类别")


class SignatureAnalysisRequest(BaseModel):
    signatures: List[str] = Field(..., description="个性签名列表", min_length=1, max_length=100)
    timeout_seconds: int = Field(default=300, description="请求超时时间（秒）", ge=30, le=1800)
    max_concurrent_requests: int = Field(default=10, description="最大并发请求数", ge=1, le=20)


class SignatureAnalysisResponse(BaseModel):
    results: List[SignatureAnalysisResult] = Field(default_factory=list, description="分析结果列表")
    total_analyzed: int = Field(..., description="分析的签名总数")
    seller_count: int = Field(..., description="识别为销售/卖家的数量")
    processing_time: float = Field(..., description="处理时间（秒）")
    model_used: str = Field(..., description="使用的AI模型")
    analysis_summary: str = Field(..., description="分析摘要")

    model_config = {"protected_namespaces": ()}
