import random
from pathlib import Path
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    BROWSER_WINDOW_SIZE: str = "1920,1080"
    LOG_STORAGE_PATH: str = "logs"
    LOG_LEVEL: str = "INFO"

    # AI服务配置
    DASHSCOPE_API_KEY: str = ""
    AI_MODEL_NAME: str = "qwen-plus"
    AI_BASE_URL: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    AI_DEFAULT_TEMPERATURE: float = 0.3
    AI_DEFAULT_MAX_TOKENS: int = 1000

    # 抖音Cookie配置
    DOUYIN_REFERENCE_COOKIES: str = ""

    # 抖音官方OA配置
    DOUYIN_OA_CLIENT_KEY: str = ""
    DOUYIN_OA_CLIENT_SECRET: str = ""
    DOUYIN_OA_REDIRECT_URI: str = ""
    FRONTEND_URL: str = "https://liando.dragonking.中国"

    # 延迟管理配置
    SEARCH_DELAY_BASE: float = 2.0
    SEARCH_DELAY_MIN: float = 1.2
    SEARCH_DELAY_MAX: float = 5.0
    SEARCH_DELAY_STRATEGY: str = "human_like"
    DELAY_DEBUG: bool = False

    # 开发者设备验证配置
    DEVELOPER_DEVICE_FINGERPRINTS: str = ""

    # 联东管理员配置
    LIANDONG_ADMIN_PASSWORD: str = "meiyoumima"

    # 代理配置
    PROXY_API_URL: str = ""
    PROXY_USERNAME: str = ""
    PROXY_PASSWORD: str = ""
    PROXY_ENABLED: bool = False

    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    REDIS_ENABLED: bool = True

    # 代理验证配置
    PROXY_CACHE_EXPIRE_MINUTES: int = 15
    PROXY_VALIDATION_TIMEOUT: int = 8
    PROXY_MAX_RESPONSE_TIME: float = 3.0

    # MySQL数据库配置
    MYSQL_HOST: str = "localhost"
    MYSQL_PORT: int = 3306
    MYSQL_USER: str = ""
    MYSQL_PASSWORD: str = ""
    MYSQL_DATABASE: str = "dyredbook"
    MYSQL_CHARSET: str = "utf8mb4"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


settings = Settings()

def ensure_directories():
    directories = [
        settings.LOG_STORAGE_PATH
    ]

    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)

ensure_directories()


class DouyinConstants:
    WAIT_TIMES = {
        "page_load": 10
    }

    # 网络安全配置
    RESTRICTED_NETWORKS = [
        "LianDo"  # 禁止连接的网络名称
    ]

    # IP地址到网络映射配置
    RESTRICTED_IP_NETWORKS = {
        "***************": "LianDo"  # IP地址到网络名称的映射
    }

    # 网络检测配置
    NETWORK_CHECK_ENABLED = True
    NETWORK_CHECK_TIMEOUT = 5  # 网络检测超时时间（秒）

    # 现代浏览器 User-Agent 列表
    USER_AGENTS = ['Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36']


    # 浏览器启动参数基础配置
    BROWSER_ARGS_BASE = [
        '--no-first-run',
        '--no-default-browser-check',
        '--disable-blink-features=AutomationControlled',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-dev-shm-usage',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-javascript-harmony-shipping',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-field-trial-config',
        '--disable-ipc-flooding-protection'
    ]

    # 默认浏览器窗口大小
    BROWSER_WINDOW_SIZE = "1280,720"

    @staticmethod
    def get_random_user_agent() -> str:
        """获取随机User-Agent"""
        return random.choice(DouyinConstants.USER_AGENTS)

    @staticmethod
    def get_browser_args(headless: bool = False, user_agent: str = None) -> list:
        """获取浏览器启动参数"""
        browser_args = DouyinConstants.BROWSER_ARGS_BASE.copy()

        # 添加窗口大小
        browser_args.append(f'--window-size={DouyinConstants.BROWSER_WINDOW_SIZE}')

        # 添加 User-Agent
        ua = user_agent if user_agent else DouyinConstants.get_random_user_agent()
        browser_args.append(f'--user-agent={ua}')

        # 添加 headless 模式
        if headless:
            browser_args.append('--headless=new')

        return browser_args

    @staticmethod
    def is_network_restricted(network_name: str) -> bool:
        """检查网络是否在限制列表中"""
        if not network_name:
            return False
        return network_name.strip() in DouyinConstants.RESTRICTED_NETWORKS

    @staticmethod
    def is_ip_restricted(ip_address: str) -> bool:
        """检查IP地址是否属于受限制网络"""
        if not ip_address:
            return False
        return ip_address.strip() in DouyinConstants.RESTRICTED_IP_NETWORKS

    @staticmethod
    def get_network_by_ip(ip_address: str) -> str:
        """根据IP地址获取对应的网络名称"""
        if not ip_address:
            return ""
        return DouyinConstants.RESTRICTED_IP_NETWORKS.get(ip_address.strip(), "")

    @staticmethod
    def get_network_security_config() -> dict:
        """获取网络安全配置"""
        return {
            "enabled": DouyinConstants.NETWORK_CHECK_ENABLED,
            "restricted_networks": DouyinConstants.RESTRICTED_NETWORKS,
            "restricted_ip_networks": DouyinConstants.RESTRICTED_IP_NETWORKS,
            "timeout": DouyinConstants.NETWORK_CHECK_TIMEOUT
        }
