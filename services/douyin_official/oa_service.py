import hashlib
import json
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict
from urllib.parse import urlenco<PERSON>, urlparse, quote
import requests

from core.logging_config import get_logger
from core.errors import ErrorCodes, DouyinServiceException
from config.settings import settings
from models.douyin_official import (
    OAAuthRequest, OAAuthUrlResponse, OACallbackRequest, OATokenInfo,
    OAUserInfo, OATokenRefreshRequest, OATokenValidateRequest, OATokenValidateResponse
)
from database.douyin_official import douyin_oa_db

logger = get_logger(__name__)


class DouyinOAService:
    def __init__(self):
        self.base_url = "https://open.douyin.com"
        self.auth_url = f"{self.base_url}/platform/oauth/connect"
        self.token_url = f"{self.base_url}/oauth/access_token"
        self.refresh_token_url = f"{self.base_url}/oauth/refresh_token"
        self.user_info_url = f"{self.base_url}/oauth/userinfo"
        self.client_key = settings.DOUYIN_OA_CLIENT_KEY
        self.client_secret = settings.DOUYIN_OA_CLIENT_SECRET
        self.redirect_uri = settings.DOUYIN_OA_REDIRECT_URI
        self._auth_states: Dict[str, Dict] = {}
        
    def generate_auth_url(self, request: OAAuthRequest) -> OAAuthUrlResponse:
        try:
            state = self._generate_state()
            scope = request.scope if request.scope else ["user_info"]

            auth_params = {
                "client_key": self.client_key,
                "response_type": "code",
                "scope": ",".join(scope),
                "redirect_uri": self.redirect_uri,
                "state": state
            }

            auth_url = f"{self.auth_url}?{urlencode(auth_params)}"

            self._auth_states[state] = {
                "client_key": self.client_key,
                "redirect_uri": self.redirect_uri,
                "scope": scope,
                "created_at": datetime.now(),
                "expires_at": datetime.now() + timedelta(minutes=10)
            }

            return OAAuthUrlResponse(
                auth_url=auth_url,
                state=state,
                expires_in=600
            )

        except Exception:
            raise DouyinServiceException("生成授权URL失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)
    
    def handle_callback(self, request: OACallbackRequest) -> OATokenInfo:
        try:

            auth_state = self._auth_states.get(request.state, {})
            if not auth_state:
                auth_state = {
                    "client_key": self.client_key,
                    "redirect_uri": self.redirect_uri
                }

            token_params = {
                "client_key": auth_state.get("client_key", self.client_key),
                "client_secret": self.client_secret,
                "code": request.code,
                "grant_type": "authorization_code"
            }

            response = requests.post(self.token_url, data=token_params, timeout=30)

            if response.status_code != 200:
                raise DouyinServiceException("授权服务响应异常", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

            token_data = response.json()
            data = token_data.get("data", {})

            if not data:
                raise DouyinServiceException("授权响应格式异常", ErrorCodes.DOUYIN_OA_AUTH_FAILED)


            data_error_code = data.get("error_code")
            if data_error_code != 0:
                raise DouyinServiceException("授权验证失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

            token_info = OATokenInfo(
                access_token=data.get("access_token"),
                refresh_token=data.get("refresh_token"),
                expires_in=data.get("expires_in", 7200),
                refresh_expires_in=data.get("refresh_expires_in", 86400),
                scope=data.get("scope", "").split(",") if data.get("scope") else [],
                open_id=data.get("open_id")
            )

            if request.state and request.state in self._auth_states:
                del self._auth_states[request.state]

            return token_info

        except DouyinServiceException:
            raise
        except Exception:
            raise DouyinServiceException("处理授权回调失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

    def save_account_to_database(self, token_info: OATokenInfo, user_info: OAUserInfo):
        try:
            douyin_oa_db.save_account(token_info, user_info)
        except Exception:
            raise DouyinServiceException("保存账号信息失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

    def get_account_info(self, open_id: str) -> dict:
        try:
            account = douyin_oa_db.get_account_by_open_id(open_id)
            if not account:
                raise DouyinServiceException("账号不存在", ErrorCodes.NOT_FOUND)


            return {
                "open_id": account.open_id,
                "union_id": account.union_id,
                "nickname": account.nickname,
                "avatar": account.avatar,
                "gender": account.gender,
                "city": account.city,
                "province": account.province,
                "country": account.country,
                "scope": account.scope,
                "created_at": account.created_at.isoformat() if account.created_at else None,
                "last_validation": account.last_validation.isoformat() if account.last_validation else None,
                "is_valid": account.is_valid
            }
        except DouyinServiceException:
            raise
        except Exception:
            raise DouyinServiceException("获取账号信息失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)
    
    def refresh_token(self, request: OATokenRefreshRequest) -> OATokenInfo:
        try:
            refresh_params = {
                "client_key": request.client_key,
                "client_secret": self.client_secret,
                "refresh_token": request.refresh_token,
                "grant_type": "refresh_token"
            }

            response = requests.post(self.refresh_token_url, data=refresh_params, timeout=30)

            if response.status_code != 200:
                raise DouyinServiceException("令牌刷新服务异常", ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED)

            token_data = response.json()
            data = token_data.get("data", {})

            if not data:
                raise DouyinServiceException("令牌刷新响应异常", ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED)

            # 检查data字段内的error_code
            data_error_code = data.get("error_code")
            if data_error_code != 0:
                raise DouyinServiceException("令牌已过期", ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED)

            token_info = OATokenInfo(
                access_token=data.get("access_token"),
                refresh_token=data.get("refresh_token"),
                expires_in=data.get("expires_in", 7200),
                refresh_expires_in=data.get("refresh_expires_in", 86400),
                scope=data.get("scope", "").split(",") if data.get("scope") else [],
                open_id=data.get("open_id")
            )



            return token_info

        except DouyinServiceException:
            raise
        except Exception:
            raise DouyinServiceException("令牌刷新服务异常", ErrorCodes.DOUYIN_OA_TOKEN_EXPIRED)

    def get_user_info(self, access_token: str, open_id: str) -> OAUserInfo:
        try:
            params = {
                "access_token": access_token,
                "open_id": open_id
            }


            response = requests.get(self.user_info_url, params=params, timeout=30)

            if response.status_code != 200:
                raise DouyinServiceException("用户信息服务异常", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

            user_data = response.json()
            data = user_data.get("data", {})

            if not data:
                raise DouyinServiceException("用户信息响应异常", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

            # 检查data字段内的error_code
            data_error_code = data.get("error_code")
            if data_error_code != 0:
                error_description = data.get("description", "未知错误")

                raise DouyinServiceException(f"用户信息访问失败: {error_description} (错误码: {data_error_code})", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

            return OAUserInfo(
                open_id=data.get("open_id"),
                union_id=data.get("union_id"),
                nickname=data.get("nickname", ""),
                avatar=data.get("avatar"),
                gender=data.get("gender"),
                city=data.get("city"),
                province=data.get("province"),
                country=data.get("country")
            )

        except DouyinServiceException:
            raise
        except Exception:
            raise DouyinServiceException("用户信息服务异常", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

    def validate_token(self, request: OATokenValidateRequest) -> OATokenValidateResponse:
        try:
            # 从数据库获取账号信息
            account = douyin_oa_db.get_account_by_open_id(request.open_id)
            if not account:
                return OATokenValidateResponse(
                    is_valid=False,
                    error_message="账号不存在"
                )

            # 使用数据库中的令牌进行验证
            self.get_user_info(account.access_token, request.open_id)

            # 计算令牌剩余时间
            expires_in = None
            scope = None
            if account.created_at:
                created_time = account.created_at
                expires_time = created_time + timedelta(seconds=account.expires_in)
                remaining_seconds = int((expires_time - datetime.now()).total_seconds())
                expires_in = max(0, remaining_seconds)
                scope = json.loads(account.scope) if account.scope else []

            # 更新验证状态
            douyin_oa_db.update_validation_status(request.open_id, True)

            return OATokenValidateResponse(
                is_valid=True,
                expires_in=expires_in,
                scope=scope
            )

        except DouyinServiceException as e:
            # 更新验证状态为失败
            douyin_oa_db.update_validation_status(request.open_id, False)
            return OATokenValidateResponse(
                is_valid=False,
                error_message=str(e)
            )
        except Exception as e:
            douyin_oa_db.update_validation_status(request.open_id, False)
            return OATokenValidateResponse(
                is_valid=False,
                error_message="令牌验证失败"
            )

    def get_oauth_success_url(self, token_info: OATokenInfo, user_info: OAUserInfo, scopes: str) -> str:
        try:
            parsed_redirect = urlparse(self.redirect_uri)
            frontend_base = f"{parsed_redirect.scheme}://{parsed_redirect.netloc}"
            user_data = {
                "open_id": token_info.open_id,
                "nickname": user_info.nickname,
                "avatar": str(user_info.avatar) if user_info.avatar else "",
                "scopes": scopes
            }
            encoded_user_data = quote(json.dumps(user_data))
            frontend_url = f"{frontend_base}/login/douyin-oa?success=true&user_data={encoded_user_data}"
            return frontend_url
        except Exception:
            raise DouyinServiceException("生成重定向URL失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

    def get_oauth_error_url(self, error_message: str = "callback_failed") -> str:
        try:
            parsed_redirect = urlparse(self.redirect_uri)
            frontend_base = f"{parsed_redirect.scheme}://{parsed_redirect.netloc}"
            encoded_error = quote(error_message)
            error_url = f"{frontend_base}/login/douyin-oa?error={encoded_error}"
            return error_url
        except Exception:
            try:
                parsed_redirect = urlparse(self.redirect_uri)
                frontend_base = f"{parsed_redirect.scheme}://{parsed_redirect.netloc}"
                return f"{frontend_base}/login/douyin-oa?error=system_error"
            except:
                raise DouyinServiceException("生成错误重定向URL失败", ErrorCodes.DOUYIN_OA_AUTH_FAILED)

    def _generate_state(self) -> str:
        return hashlib.md5(f"{secrets.token_hex(16)}{time.time()}".encode()).hexdigest()


douyin_oa_service = DouyinOAService()
