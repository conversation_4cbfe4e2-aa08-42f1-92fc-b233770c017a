import requests
import time
from typing import Dict, Any
from datetime import datetime

from core.errors import ErrorCodes
from core.responses import success_response, error_response
from models.douyin import SearchRequest, VideoInfo, SearchResult
from utils.douyin_http_client import douyin_http_client
from utils.delay_manager import search_delay_manager
from database.douyin import douyin_video_db


class DouyinSearchServiceV3:
    def __init__(self):
        self.generate_search_url_api = "http://101.201.104.34:8001/douyin/search-url"

    def _generate_douyin_search_url(self, keyword: str, offset: int, timeout: int) -> str:
        try:
            payload = {
                "keyword": keyword,
                "offset": str(offset)
            }
            response = requests.post(
                self.generate_search_url_api,
                json=payload,
                timeout=timeout
            )
            if response.status_code != 200:
                return ""
            response_data = response.json()
            return response_data.get("complete_url", "")
        except Exception:
            return ""

    def _make_search_request(self, keyword: str, offset: int, timeout: int) -> dict:
        try:
            douyin_url = self._generate_douyin_search_url(keyword, offset, timeout)
            if not douyin_url:
                return error_response(
                    code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                    message="获取搜索接口失败"
                )

            max_retries = 10
            for attempt in range(max_retries):
                response = douyin_http_client.get(
                    url=douyin_url,
                    cookies=None,
                    referer='https://www.douyin.com/root/search',
                    timeout=timeout
                )

                if not response.get('success'):
                    return response

                response_data = response['data']

                if (response_data.get('data') == [] and
                    response_data.get('search_nil_info') and
                    attempt < max_retries - 1):
                    time.sleep(1 + attempt)
                    continue

                return success_response(data=response_data)

            return success_response(data=response_data)

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                message="搜索请求异常"
            )

    def search_videos(self, request: SearchRequest) -> dict:
        if request.pages and request.pages >= 1:
            return self.search_videos_batch(request)

        response = self._make_search_request(
            keyword=request.keyword,
            offset=request.offset,
            timeout=request.timeout_seconds
        )

        if not response.get('success'):
            return response

        return self._parse_search_response(response['data'], request)

    def search_videos_batch(self, request: SearchRequest) -> dict:
        try:
            search_delay_manager.reset_progressive()

            target_count = request.pages * request.per_page
            all_videos = []
            seen_video_ids = set()
            errors = []
            failed_pages = []
            page = 0
            max_pages = min(2000, target_count * 5)
            consecutive_empty_pages = 0
            max_consecutive_empty = 15

            while len(all_videos) < target_count and page < max_pages and consecutive_empty_pages < max_consecutive_empty:
                offset = page * request.per_page

                try:
                    response = self._make_search_request(
                        keyword=request.keyword,
                        offset=offset,
                        timeout=request.timeout_seconds
                    )

                    if not response.get('success'):
                        error_msg = f"第{page + 1}页请求失败: {response.get('message', '未知错误')}"
                        errors.append(error_msg)
                        failed_pages.append(page)
    
                    else:
                        page_videos = self._extract_videos_from_response(response['data'])
                        if page_videos:
                            new_videos = []
                            for video in page_videos:
                                if video['aweme_id'] not in seen_video_ids:
                                    seen_video_ids.add(video['aweme_id'])
                                    new_videos.append(video)

                            if new_videos:
                                all_videos.extend(new_videos)
                                consecutive_empty_pages = 0
                            else:
                                consecutive_empty_pages += 1
                        else:
                            consecutive_empty_pages += 1

                except Exception as e:
                    error_msg = f"第{page + 1}页搜索异常: {str(e)}"
                    errors.append(error_msg)
                    failed_pages.append(page)

                page += 1

                if page < max_pages and len(all_videos) < target_count and consecutive_empty_pages < max_consecutive_empty:
                    search_delay_manager.delay()


            if len(all_videos) < target_count and failed_pages:
                retry_count = 0
                max_retry_rounds = 5

                while len(all_videos) < target_count and failed_pages and retry_count < max_retry_rounds:
                    retry_count += 1
                    current_failed_pages = failed_pages.copy()
                    failed_pages.clear()

                    for retry_page in current_failed_pages:
                        if len(all_videos) >= target_count:
                            break

                        try:
                            offset = retry_page * request.per_page
                            response = self._make_search_request(
                                keyword=request.keyword,
                                offset=offset,
                                timeout=request.timeout_seconds
                            )

                            if response.get('success'):
                                page_videos = self._extract_videos_from_response(response['data'])
                                if page_videos:
                                    new_videos = []
                                    for video in page_videos:
                                        if video['aweme_id'] not in seen_video_ids:
                                            seen_video_ids.add(video['aweme_id'])
                                            new_videos.append(video)
                                    if new_videos:
                                        all_videos.extend(new_videos)
                            else:
                                failed_pages.append(retry_page)
                        except Exception:
                            failed_pages.append(retry_page)
                        if len(all_videos) < target_count:
                            search_delay_manager.delay()

            final_videos = all_videos[:target_count]

            search_result = SearchResult(
                videos=final_videos,
                total_count=len(final_videos),
                has_more=False,
                cursor=None
            )

            delay_stats = search_delay_manager.get_stats()

            database_stats = self._save_videos_to_database(request.keyword, final_videos)

            success_message = f"批量搜索完成，找到 {len(final_videos)} 个相关视频"
            if errors:
                success_message += f"，{len(errors)}页获取失败"

            return success_response(
                data={
                    "keyword": request.keyword,
                    "result": search_result.model_dump(),
                    "search_time": datetime.now().isoformat(),
                    "video_ids": [video['aweme_id'] for video in final_videos],
                    "pages_requested": request.pages,
                    "pages_failed": len(errors),
                    "errors": errors if errors else None,
                    "delay_stats": delay_stats,
                    "database_stats": database_stats
                },
                message=success_message
            )

        except Exception as e:
            return error_response(
                code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                message="批量搜索服务异常，请稍后重试"
            )

    def _extract_videos_from_response(self, response_data: Dict[str, Any]) -> list:
        if response_data.get('status_code') != 0:
            return []

        data = response_data.get('data', [])
        videos = []
        for item in data:
            aweme_info = item.get('aweme_info', {})
            if aweme_info:
                video_info = self._extract_video_info(aweme_info)
                if video_info:
                    videos.append(video_info.model_dump())

        return videos

    def _parse_search_response(self, response_data: Dict[str, Any], request: SearchRequest) -> dict:
        try:
            if response_data.get('status_code') != 0:
                return error_response(
                    code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                    message="搜索失败，请稍后重试"
                )

            data = response_data.get('data', [])
            videos = []

            for item in data:
                aweme_info = item.get('aweme_info', {})
                if aweme_info:
                    video_info = self._extract_video_info(aweme_info)
                    if video_info:
                        videos.append(video_info)
                        if len(videos) >= request.count:
                            break

            search_result = SearchResult(
                videos=videos,
                total_count=len(videos),
                has_more=response_data.get('has_more', False),
                cursor=response_data.get('cursor')
            )

            video_dicts = [video.model_dump() if hasattr(video, 'model_dump') else video for video in videos]
            database_stats = self._save_videos_to_database(request.keyword, video_dicts)

            return success_response(
                data={
                    "keyword": request.keyword,
                    "result": search_result.model_dump(),
                    "search_time": datetime.now().isoformat(),
                    "database_stats": database_stats
                },
                message=f"搜索完成，找到 {len(videos)} 个相关视频"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                message="搜索结果处理异常"
            )

    def _extract_video_info(self, aweme_info: Dict[str, Any]) -> VideoInfo:
        try:
            aweme_id = aweme_info.get('aweme_id', '')
            if not aweme_id:
                return None

            author = aweme_info.get('author', {})
            statistics = aweme_info.get('statistics', {})

            return VideoInfo(
                aweme_id=aweme_id,
                desc=aweme_info.get('desc', ''),
                author_nickname=author.get('nickname', ''),
                author_unique_id=author.get('unique_id', ''),
                create_time=aweme_info.get('create_time'),
                statistics=statistics,
                douyin_link=f"https://www.douyin.com/video/{aweme_id}"
            )

        except Exception:
            return None

    def _save_videos_to_database(self, keyword: str, videos: list) -> dict:
        try:
            if not videos:
                return {
                    "inserted_count": 0,
                    "skipped_count": 0,
                    "error_count": 0,
                    "message": "没有视频数据需要保存"
                }

            video_dicts = []
            for video in videos:
                if isinstance(video, dict):
                    video_dicts.append(video)
                else:
                    video_dicts.append(video.model_dump() if hasattr(video, 'model_dump') else video.__dict__)

            db_result = douyin_video_db.batch_insert_videos(keyword, video_dicts)
            return db_result

        except ValueError:
            return {
                "inserted_count": 0,
                "skipped_count": 0,
                "error_count": 0,
                "message": "参数错误"
            }
        except Exception:
            return {
                "inserted_count": 0,
                "skipped_count": 0,
                "error_count": 0,
                "message": "数据库保存异常"
            }


douyin_search_service_v3 = DouyinSearchServiceV3()
