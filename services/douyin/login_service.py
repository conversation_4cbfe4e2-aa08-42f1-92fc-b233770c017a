
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List
import threading
from concurrent.futures import ThreadPoolExecutor
import time

import nodriver as uc
from nodriver import cdp

from config.settings import DouyinConstants
from core.errors import (
    ErrorCodes, DouyinServiceException, DouyinSessionException
)
from core.logging_config import get_logger
from models.douyin import (
    LoginSession, LoginStatus, CookieData, LoginStartRequest,
    CookieValidationRequest, CookieValidationResponse
)
from utils.douyin_http_client import douyin_http_client


logger = get_logger(__name__)


class DouyinLoginService:
    def __init__(self):
        self.active_sessions: Dict[str, LoginSession] = {}
        self.browser_instances: Dict[str, tuple] = {}
        self.cookie_requests: Dict[str, bool] = {}
        self.cookie_results: Dict[str, tuple] = {}
        self.executor = ThreadPoolExecutor(max_workers=5)
        self._cleanup_lock = threading.Lock()
    async def _create_browser_async(self, headless: bool = False, user_agent: str = None) -> tuple:
        try:
            browser_args = DouyinConstants.get_browser_args(headless, user_agent)

            browser = await uc.start(
                headless=headless,
                browser_args=browser_args,
                user_data_dir=None,
            )

            tab = await browser.get()
            return browser, tab

        except Exception as e:
            logger.error(f"浏览器初始化失败: {str(e)}")
            raise DouyinServiceException("浏览器启动失败", ErrorCodes.DOUYIN_BROWSER_ERROR)

    async def _async_browser_operations(self, headless: bool = False, user_agent: str = None) -> tuple:
        try:
            browser, tab = await self._create_browser_async(headless, user_agent)
            target_url = "https://www.douyin.com/jingxuan?modal_id=7513426459424345363"
            await tab.get(target_url)
            await asyncio.sleep(3)
            return browser, tab

        except Exception as e:
            logger.error(f"浏览器操作失败: {str(e)}")
            raise


    
    def start_login_session(self, request: LoginStartRequest) -> LoginSession:
        try:
            expires_at = datetime.now() + timedelta(minutes=request.timeout_minutes)
            session = LoginSession(
                expires_at=expires_at,
                status=LoginStatus.PENDING
            )

            self.active_sessions[session.session_id] = session

            self.executor.submit(
                self._start_browser_session,
                session.session_id,
                request.headless
            )

            return session

        except Exception:
            raise DouyinServiceException("启动登录会话失败")
    
    def _start_browser_session(self, session_id: str, headless: bool = False):
        try:
            asyncio.run(self._browser_session_loop(session_id, headless))
        except DouyinServiceException as e:
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = LoginStatus.FAILED
                self.active_sessions[session_id].error_message = str(e)
            self._cleanup_browser(session_id)
        except Exception as e:
            logger.error(f"启动浏览器会话失败: {str(e)}")
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = LoginStatus.FAILED
                self.active_sessions[session_id].error_message = "启动浏览器失败"
            self._cleanup_browser(session_id)

    async def _browser_session_loop(self, session_id: str, headless: bool = False):
        try:
            session = self.active_sessions.get(session_id)
            if not session:
                return

            self.cookie_requests[session_id] = False
            self.cookie_results[session_id] = None

            browser, tab = await self._async_browser_operations(headless)
            self.browser_instances[session_id] = (browser, tab)

            session.status = LoginStatus.READY
            session.updated_at = datetime.now()
            session.browser_pid = None

            timeout = 600
            start_time = time.time()

            while session_id in self.active_sessions and time.time() - start_time < timeout:
                if self.cookie_requests.get(session_id, False):
                    try:
                        result = await self._get_cookies_async(tab)
                        self.cookie_results[session_id] = result
                        break
                    except Exception as e:
                        logger.error(f"Cookie获取失败: {str(e)}")
                        self.cookie_results[session_id] = (False, f"Cookie获取失败: {str(e)}")
                        break

                await asyncio.sleep(0.5)

            if time.time() - start_time >= timeout:
                self.cookie_results[session_id] = (False, "等待用户登录超时")

        except Exception as e:
            logger.error(f"浏览器会话循环错误: {str(e)}")
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = LoginStatus.FAILED
                self.active_sessions[session_id].error_message = f"浏览器会话失败: {str(e)}"
            self.cookie_results[session_id] = (False, f"浏览器会话失败: {str(e)}")

    async def _get_cookies_async(self, tab) -> tuple:
        try:
            try:
                await tab.evaluate("window.location.href")
            except Exception as e:
                logger.error(f"浏览器状态检查失败: {str(e)}")
                return False, f"浏览器连接已断开: {str(e)}"

            try:
                cookies = await tab.send(cdp.storage.get_cookies())
                if not cookies:
                    return False, "未获取到登录Cookie，请确保已完成登录"
            except Exception as e:
                logger.error(f"Cookie获取失败: {str(e)}")
                return False, f"Cookie获取失败: {str(e)}"

            try:
                processed_cookies = [
                    CookieData(
                        name=cookie.name if hasattr(cookie, 'name') else '',
                        value=cookie.value if hasattr(cookie, 'value') else '',
                        domain=cookie.domain if hasattr(cookie, 'domain') else '',
                        path=cookie.path if hasattr(cookie, 'path') else '/',
                        secure=cookie.secure if hasattr(cookie, 'secure') else False,
                        http_only=cookie.http_only if hasattr(cookie, 'http_only') else False,
                        same_site=cookie.same_site if hasattr(cookie, 'same_site') else None
                    )
                    for cookie in cookies
                ]

                # 验证关键cookie完整性
                logger.info(f"登录获取到的Cookie总数: {len(processed_cookies)}")
                key_cookies = ['UIFID', 'UIFID_TEMP', 'sessionid', 'sid_tt', 'uid_tt']
                found_keys = []
                for cookie in processed_cookies:
                    if cookie.name in key_cookies:
                        found_keys.append(cookie.name)
                        logger.info(f"找到关键Cookie: {cookie.name} = {cookie.value[:50]}...")
                logger.info(f"找到的关键Cookie: {found_keys}")

                # 检查UIFID状态
                has_uifid = any(cookie.name == 'UIFID' for cookie in processed_cookies)
                has_uifid_temp = any(cookie.name == 'UIFID_TEMP' for cookie in processed_cookies)
                logger.info(f"UIFID存在: {has_uifid}, UIFID_TEMP存在: {has_uifid_temp}")

                if not has_uifid and not has_uifid_temp:
                    logger.warning("登录Cookie中缺少UIFID和UIFID_TEMP，可能影响搜索功能")

                return True, processed_cookies

            except Exception as e:
                logger.error(f"Cookie数据处理失败: {str(e)}")
                return False, f"Cookie数据处理失败: {str(e)}"

        except Exception as e:
            logger.error(f"Cookie获取过程发生错误: {str(e)}", exc_info=True)
            return False, f"Cookie获取失败: {str(e)}"


    
    def get_session_status(self, session_id: str) -> LoginSession:
        session = self.active_sessions.get(session_id)
        if not session:
            raise DouyinSessionException("登录会话不存在")

        if datetime.now() > session.expires_at:
            session.status = LoginStatus.EXPIRED
            self._cleanup_session(session_id)
            raise DouyinSessionException("登录会话已过期")

        return session

    def confirm_login(self, session_id: str) -> bool:
        try:
            # 步骤1：检查会话和浏览器实例
            session = self.active_sessions.get(session_id)

            if not session:
                return False

            if session_id not in self.browser_instances:
                if session_id in self.active_sessions:
                    self.active_sessions[session_id].status = LoginStatus.FAILED
                    self.active_sessions[session_id].error_message = "浏览器实例已失效"
                return False

            try:
                self.cookie_requests[session_id] = True
                timeout = 30
                start_time = time.time()

                while time.time() - start_time < timeout:
                    if session_id in self.cookie_results and self.cookie_results[session_id] is not None:
                        result = self.cookie_results[session_id]
                        success, data = result

                        if not success:
                            if session_id in self.active_sessions:
                                self.active_sessions[session_id].status = LoginStatus.FAILED
                                self.active_sessions[session_id].error_message = data
                            return False

                        session.cookies = data
                        break

                    time.sleep(0.5)
                else:
                    if session_id in self.active_sessions:
                        self.active_sessions[session_id].status = LoginStatus.FAILED
                        self.active_sessions[session_id].error_message = "Cookie获取超时，请重试"
                    return False

            except Exception as e:
                logger.error(f"Cookie获取失败: {str(e)}")
                if session_id in self.active_sessions:
                    self.active_sessions[session_id].status = LoginStatus.FAILED
                    self.active_sessions[session_id].error_message = f"Cookie获取失败: {str(e)}"
                return False

            session.status = LoginStatus.SUCCESS
            session.updated_at = datetime.now()
            return True

        except Exception as e:
            logger.error(f"登录确认过程发生未预期错误: {str(e)}", exc_info=True)
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = LoginStatus.FAILED
                self.active_sessions[session_id].error_message = f"登录确认失败: {str(e)}"
            return False

    def get_session_cookies(self, session_id: str) -> List[CookieData]:
        session = self.active_sessions.get(session_id)
        if session and session.status == LoginStatus.SUCCESS:
            return session.cookies
        return []

    def cancel_session(self, session_id: str) -> bool:
        try:
            if session_id in self.active_sessions:
                self.active_sessions[session_id].status = LoginStatus.CANCELLED
                self.active_sessions[session_id].updated_at = datetime.now()

            self._cleanup_session(session_id)
            return True

        except Exception as e:
            logger.error(f"取消会话失败: {str(e)}")
            return False

    def _cleanup_session(self, session_id: str):
        with self._cleanup_lock:
            try:
                self._cleanup_browser(session_id)

                if session_id in self.cookie_requests:
                    del self.cookie_requests[session_id]
                if session_id in self.cookie_results:
                    del self.cookie_results[session_id]

                if session_id in self.active_sessions:
                    del self.active_sessions[session_id]

            except Exception as e:
                logger.error(f"清理会话资源失败: {str(e)}")

    def _cleanup_browser(self, session_id: str):
        try:
            if session_id in self.browser_instances:
                browser_data = self.browser_instances[session_id]
                try:
                    browser, tab = browser_data
                    asyncio.run(self._close_browser_async(browser, tab))
                except Exception as e:
                    logger.error(f"关闭浏览器时出错: {str(e)}")
                finally:
                    del self.browser_instances[session_id]

        except Exception as e:
            logger.error(f"清理浏览器实例失败: {str(e)}")

    async def _close_browser_async(self, browser, tab):
        try:
            if tab:
                try:
                    await tab.close()
                except:
                    pass

            if browser:
                try:
                    await browser.stop()
                except:
                    pass

        except Exception as e:
            logger.error(f"关闭浏览器失败: {str(e)}")

    def cleanup_expired_sessions(self):
        current_time = datetime.now()
        expired_sessions = [
            session_id for session_id, session in self.active_sessions.items()
            if current_time > session.expires_at
        ]

        for session_id in expired_sessions:
            self._cleanup_session(session_id)

    def get_active_sessions_count(self) -> int:
        return len(self.active_sessions)

    def shutdown(self):
        session_ids = list(self.active_sessions.keys())
        for session_id in session_ids:
            self._cleanup_session(session_id)

        self.executor.shutdown(wait=True)

    def validate_cookies(self, request: CookieValidationRequest) -> CookieValidationResponse:
        """验证Cookie的有效性"""
        try:
            api_url = "https://www.douyin.com/aweme/v1/web/aweme/post/"
            params = {'aid': '6383'}

            response = douyin_http_client.get(
                url=api_url,
                cookies=request.cookies,
                params=params,
                referer='https://www.douyin.com/',
                timeout=request.timeout_seconds
            )

            if not response.get('success'):
                return CookieValidationResponse(
                    is_valid=False,
                    error_message="登录状态验证失败，请重新登录",
                    status_code=ErrorCodes.DOUYIN_API_ERROR
                )

            response_data = response.get('data')
            if not response_data:
                return CookieValidationResponse(
                    is_valid=False,
                    error_message="登录状态已失效，请重新登录",
                    status_code=ErrorCodes.DOUYIN_AUTH_FAILED
                )

            if 'status_code' in response_data:
                if response_data['status_code'] == 0:
                    aweme_list = response_data.get('aweme_list', [])

                    validation_data = {
                        'status_code': response_data['status_code'],
                        'aweme_count': len(aweme_list),
                        'has_more': response_data.get('has_more', 0),
                        'validation_method': 'douyin_api',
                        'api_endpoint': 'aweme/v1/web/aweme/post'
                    }

                    if aweme_list:
                        first_aweme = aweme_list[0]
                        if 'author' in first_aweme:
                            author = first_aweme['author']
                            user_info = {
                                'uid': author.get('uid'),
                                'nickname': author.get('nickname'),
                                'sec_uid': author.get('sec_uid'),
                                'avatar_thumb': author.get('avatar_thumb')
                            }
                            validation_data['user_info'] = user_info

                    return CookieValidationResponse(
                        is_valid=True,
                        user_data=validation_data,
                        status_code=ErrorCodes.SUCCESS
                    )
                else:
                    return CookieValidationResponse(
                        is_valid=False,
                        error_message="登录状态已失效，请重新登录",
                        status_code=ErrorCodes.DOUYIN_AUTH_FAILED
                    )
            else:
                if 'aweme_list' in response_data or 'data' in response_data:
                    return CookieValidationResponse(
                        is_valid=True,
                        user_data={
                            'validation_method': 'douyin_api_fallback',
                            'response_keys': list(response_data.keys())
                        },
                        status_code=ErrorCodes.SUCCESS
                    )
                else:
                    return CookieValidationResponse(
                        is_valid=False,
                        error_message="登录状态验证失败，请重新登录",
                        status_code=ErrorCodes.DOUYIN_API_ERROR
                    )

        except Exception:
            return CookieValidationResponse(
                is_valid=False,
                error_message="登录状态验证失败，请重新登录",
                status_code=ErrorCodes.DOUYIN_API_ERROR
            )


douyin_login_service = DouyinLoginService()
