import json
from typing import Dict, Any

from core.errors import ErrorCodes
from core.responses import success_response, error_response
from models.douyin import UserInfoRequest, DouyinUserInfo, UserInfoResult
from utils.douyin_http_client import douyin_http_client



class DouyinUserInfoService:
    def __init__(self):
        self.user_info_url = "https://www.douyin.com/aweme/v1/web/im/user/info/"
        self.user_info_referer = "https://www.douyin.com/"

    def get_user_info(self, request: UserInfoRequest) -> dict:
        """通过sec_uid精准获取用户信息"""
        form_data = self._build_form_data(request)
        extra_headers = self._build_extra_headers()

        response = douyin_http_client.post(
            url=self.user_info_url,
            cookies=request.cookies,
            data=form_data,
            referer=self.user_info_referer,
            timeout=request.timeout_seconds,
            extra_headers=extra_headers
        )

        if not response.get('success'):
            return response

        return self._parse_user_info(response['data'], request)

    def _build_extra_headers(self) -> Dict[str, str]:
        return {
            'X-Secsdk-Csrf-Token': 'DOWNGRADE'
        }

    def _build_form_data(self, request: UserInfoRequest) -> Dict[str, Any]:
        sec_user_ids = request.sec_uid if isinstance(request.sec_uid, list) else [request.sec_uid]
        return {'sec_user_ids': json.dumps(sec_user_ids)}

    def _parse_user_info(self, response_data: Dict[str, Any], request: UserInfoRequest) -> dict:
        try:
            if response_data.get('status_code') != 0:
                return error_response(
                    code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                    message="获取用户信息失败，请稍后重试"
                )
            return self._extract_user_info(response_data, request)
        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                message="用户信息处理异常"
            )

    def _extract_user_info(self, response_data: Dict[str, Any], request: UserInfoRequest) -> dict:
        try:
            users_data = response_data.get('data', [])
            requested_uids = request.sec_uid if isinstance(request.sec_uid, list) else [request.sec_uid]

            if not users_data:
                result = UserInfoResult(
                    users=[],
                    total_requested=len(requested_uids),
                    total_found=0,
                    message="未找到任何用户信息"
                )
                return success_response(data=result.model_dump(), message="未找到用户信息")

            found_users = []
            for user_data in users_data:
                short_id = user_data.get('short_id', '')
                unique_id = user_data.get('unique_id', '')
                douyin_id = unique_id if unique_id else short_id

                douyin_user = DouyinUserInfo(
                    short_id=douyin_id,
                    nickname=user_data.get('nickname', ''),
                    signature=user_data.get('signature', ''),
                    uid=user_data.get('uid', ''),
                    sec_uid=user_data.get('sec_uid', ''),
                    avatar_url=self._get_avatar_url(user_data)
                )
                found_users.append(douyin_user)

            result = UserInfoResult(
                users=found_users,
                total_requested=len(requested_uids),
                total_found=len(found_users),
                message=f"成功获取 {len(found_users)}/{len(requested_uids)} 个用户信息"
            )

            return success_response(
                data=result.model_dump(),
                message=f"成功获取用户信息，找到 {len(found_users)} 个用户"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_SEARCH_FAILED,
                message="用户信息提取失败"
            )

    def _get_avatar_url(self, user_info: Dict[str, Any]) -> str:
        avatar_larger = user_info.get('avatar_larger', {})
        if avatar_larger and avatar_larger.get('url_list'):
            return avatar_larger['url_list'][0]

        avatar_medium = user_info.get('avatar_medium', {})
        if avatar_medium and avatar_medium.get('url_list'):
            return avatar_medium['url_list'][0]

        avatar_thumb = user_info.get('avatar_thumb', {})
        if avatar_thumb and avatar_thumb.get('url_list'):
            return avatar_thumb['url_list'][0]

        return ""




douyin_user_info_service = DouyinUserInfoService()
