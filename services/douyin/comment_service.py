import requests
from typing import Dict, Any
from datetime import datetime

from core.errors import ErrorCodes
from core.responses import success_response, error_response
from models.douyin import CommentRequest, CommentInfo, CommentResult, VideoFilterInfo
from utils.douyin_http_client import douyin_http_client


class DouyinCommentService:
    def __init__(self):
        self.generate_url_api = "http://101.201.104.34:8001/douyin/generate-url"

    def _calculate_months_difference(self, timestamp: int) -> float:
        try:
            video_time = datetime.fromtimestamp(timestamp)
            current_time = datetime.now()

            months_diff = (current_time.year - video_time.year) * 12 + (current_time.month - video_time.month)

            if current_time.day < video_time.day:
                months_diff -= 1

            return months_diff
        except Exception:
            return 999

    def _calculate_required_pages(self, total_comments: int, max_limit: int) -> tuple[int, int]:
        actual_fetch_count = min(total_comments, max_limit)
        pages_needed = (actual_fetch_count + 9) // 10

        return pages_needed, actual_fetch_count

    def _should_skip_video(self, video_info: VideoFilterInfo, max_months: int, min_comments: int) -> tuple[bool, str]:
        """判断是否应该跳过视频获取评论

        Returns:
            tuple: (should_skip, reason)
        """

        if video_info.create_time is not None:
            months_old = self._calculate_months_difference(video_info.create_time)
            if months_old > max_months:
                return True, f"视频发布时间超过{max_months}个月（{months_old:.1f}个月前）"


        if video_info.comment_count is not None:
            if video_info.comment_count < min_comments:
                return True, f"评论数量不足（{video_info.comment_count}条，最少需要{min_comments}条）"

        return False, ""

    def _fetch_all_comments_for_video(self, request: CommentRequest) -> dict:
        try:
            all_comments = []
            current_cursor = "0"
            pages_fetched = 0
            total_comments = 0
            is_limited = False


            first_request = CommentRequest(
                aweme_id=request.aweme_id,
                cursor=current_cursor,
                cookies=request.cookies,
                timeout_seconds=request.timeout_seconds,
                fetch_all_comments=False
            )

            first_result = self._fetch_single_page_comments(first_request)
            if not first_result.get('success'):
                return first_result

            first_data = first_result['data']
            total_comments = first_data.get('total', 0)


            pages_needed, actual_fetch_count = self._calculate_required_pages(
                total_comments, request.max_comments_limit
            )


            if total_comments > request.max_comments_limit:
                is_limited = True


            all_comments.extend(first_data.get('comments', []))
            pages_fetched = 1
            current_cursor = first_data.get('cursor', '0')
            has_more = first_data.get('has_more', False)


            while pages_fetched < pages_needed and has_more and len(all_comments) < actual_fetch_count:

                next_request = CommentRequest(
                    aweme_id=request.aweme_id,
                    cursor=current_cursor,
                    cookies=request.cookies,
                    timeout_seconds=request.timeout_seconds,
                    fetch_all_comments=False
                )

                next_result = self._fetch_single_page_comments(next_request)
                if not next_result.get('success'):
                    break

                next_data = next_result['data']
                next_comments = next_data.get('comments', [])


                remaining_slots = actual_fetch_count - len(all_comments)
                if len(next_comments) > remaining_slots:
                    next_comments = next_comments[:remaining_slots]

                all_comments.extend(next_comments)
                pages_fetched += 1
                current_cursor = next_data.get('cursor', '0')
                has_more = next_data.get('has_more', False)


            result = CommentResult(
                comments=all_comments,
                has_more=has_more and len(all_comments) < total_comments,
                cursor=current_cursor,
                total=total_comments,
                fetched_count=len(all_comments),
                is_limited=is_limited,
                pages_fetched=pages_fetched,
                message=f"成功获取 {len(all_comments)} 条评论（共 {total_comments} 条，获取了 {pages_fetched} 页）"
            )

            return success_response(
                data=result.model_dump(),
                message=f"成功获取视频 {request.aweme_id} 的完整评论数据"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="完整评论获取异常"
            )

    def _process_video_list(self, request: CommentRequest) -> dict:
        try:
            all_comments = []
            processed_videos = []
            skipped_videos = []
            filter_stats = {
                "total_videos": len(request.video_list),
                "processed": 0,
                "skipped_time": 0,
                "skipped_comments": 0,
                "failed": 0
            }

            for video_info in request.video_list:

                should_skip, skip_reason = self._should_skip_video(
                    video_info,
                    request.max_months_old,
                    request.min_comment_count
                )

                if should_skip:
                    skipped_videos.append({
                        "aweme_id": video_info.aweme_id,
                        "reason": skip_reason,
                        "create_time": video_info.create_time,
                        "comment_count": video_info.comment_count
                    })

                    if "时间" in skip_reason:
                        filter_stats["skipped_time"] += 1
                    elif "评论" in skip_reason:
                        filter_stats["skipped_comments"] += 1
                    continue


                single_request = CommentRequest(
                    aweme_id=video_info.aweme_id,
                    cursor=request.cursor,
                    cookies=request.cookies,
                    timeout_seconds=request.timeout_seconds,
                    fetch_all_comments=request.fetch_all_comments,
                    max_comments_limit=request.max_comments_limit
                )


                try:
                    result = self._get_single_video_comments(single_request)
                    if result.get('success'):
                        video_comments = result['data'].get('comments', [])
                        all_comments.extend(video_comments)
                        processed_videos.append(video_info.aweme_id)
                        filter_stats["processed"] += 1
                    else:
                        skipped_videos.append({
                            "aweme_id": video_info.aweme_id,
                            "reason": f"获取评论失败: {result.get('message', '未知错误')}",
                            "create_time": video_info.create_time,
                            "comment_count": video_info.comment_count
                        })
                        filter_stats["failed"] += 1
                except Exception as e:
                    skipped_videos.append({
                        "aweme_id": video_info.aweme_id,
                        "reason": f"处理异常: {str(e)}",
                        "create_time": video_info.create_time,
                        "comment_count": video_info.comment_count
                    })
                    filter_stats["failed"] += 1


            fetch_mode_text = "完整评论" if request.fetch_all_comments else "首页评论"
            result = CommentResult(
                comments=all_comments,
                has_more=False,
                cursor="0",
                total=len(all_comments),
                fetched_count=len(all_comments),
                is_limited=False,
                pages_fetched=0,
                message=f"批量处理完成，成功获取 {filter_stats['processed']} 个视频的 {len(all_comments)} 条{fetch_mode_text}",
                processed_videos=processed_videos,
                skipped_videos=skipped_videos,
                filter_stats=filter_stats
            )

            return success_response(
                data=result.model_dump(),
                message=f"批量评论获取完成，处理了 {filter_stats['processed']} 个视频"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="批量评论处理异常，请稍后重试"
            )

    def get_comments(self, request: CommentRequest) -> dict:
        try:
            if request.video_list:
                return self._process_video_list(request)
            return self._get_single_video_comments(request)

        except requests.exceptions.Timeout:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="评论请求超时，请检查网络连接"
            )
        except requests.exceptions.RequestException:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="网络连接失败，请检查您的网络"
            )
        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="评论服务异常，请稍后重试"
            )

    def _get_single_video_comments(self, request: CommentRequest) -> dict:
        try:

            if request.fetch_all_comments:
                return self._fetch_all_comments_for_video(request)


            return self._fetch_single_page_comments(request)

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="评论获取异常"
            )

    def _fetch_single_page_comments(self, request: CommentRequest) -> dict:
        try:
            douyin_url = self._generate_douyin_url(request)
            if not douyin_url:
                return error_response(
                    code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                    message="获取评论接口失败"
                )

            return self._fetch_comments(douyin_url, request)

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="单页评论获取异常"
            )

    def _generate_douyin_url(self, request: CommentRequest) -> str:
        try:
            payload = {
                "aweme_id": request.aweme_id,
                "cursor": request.cursor
            }
            response = requests.post(
                self.generate_url_api,
                json=payload,
                timeout=request.timeout_seconds
            )
            if response.status_code != 200:
                return ""
            response_data = response.json()
            return response_data.get("complete_url", "")

        except Exception:
            return ""

    def _fetch_comments(self, douyin_url: str, request: CommentRequest) -> dict:
        response = douyin_http_client.get(
            url=douyin_url,
            cookies=request.cookies,
            timeout=request.timeout_seconds
        )

        if not response.get('success'):
            return response

        return self._parse_comment_response(response['data'], request)

    def _parse_comment_response(self, response_data: Dict[str, Any], request: CommentRequest) -> dict:
        try:
            if response_data.get('status_code') != 0:
                return error_response(
                    code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                    message="评论获取失败，请稍后重试"
                )

            return self._extract_comments(response_data, request)

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="评论数据处理异常"
            )

    def _extract_comments(self, response_data: Dict[str, Any], request: CommentRequest) -> dict:
        try:
            comments_data = response_data.get('comments', [])
            has_more = response_data.get('has_more', False)
            cursor = response_data.get('cursor', '0')
            total = response_data.get('total', 0)

            comments = []
            for comment_data in comments_data:
                user_info = comment_data.get('user', {})
                unique_id = user_info.get('unique_id', '')
                short_id = user_info.get('short_id', '')
                douyin_id = unique_id if unique_id else short_id

                comment = CommentInfo(
                    cid=comment_data.get('cid', ''),
                    aweme_id=request.aweme_id,
                    text=comment_data.get('text', ''),
                    user_nickname=user_info.get('nickname', ''),
                    user_uid=user_info.get('uid', ''),
                    user_short_id=douyin_id,
                    user_sec_uid=user_info.get('sec_uid', ''),
                    user_avatar=self._get_user_avatar(user_info),
                    create_time=comment_data.get('create_time', 0),
                    digg_count=comment_data.get('digg_count', 0),
                    reply_count=comment_data.get('reply_comment_total', 0),
                    is_author=comment_data.get('is_author', False),
                    ip_label=comment_data.get('ip_label', '')
                )
                comments.append(comment)

            result = CommentResult(
                comments=comments,
                has_more=has_more,
                cursor=str(cursor),
                total=total,
                fetched_count=len(comments),
                is_limited=False,
                pages_fetched=1,
                message=f"成功获取 {len(comments)} 条评论"
            )

            return success_response(
                data=result.model_dump(),
                message=f"成功获取视频 {request.aweme_id} 的评论数据"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_COMMENT_FAILED,
                message="评论信息提取失败"
            )

    def _get_user_avatar(self, user_info: Dict[str, Any]) -> str:
        avatar_larger = user_info.get('avatar_larger', {})
        if avatar_larger and avatar_larger.get('url_list'):
            return avatar_larger['url_list'][0]
        
        avatar_medium = user_info.get('avatar_medium', {})
        if avatar_medium and avatar_medium.get('url_list'):
            return avatar_medium['url_list'][0]
        
        avatar_thumb = user_info.get('avatar_thumb', {})
        if avatar_thumb and avatar_thumb.get('url_list'):
            return avatar_thumb['url_list'][0]
        
        return ""


douyin_comment_service = DouyinCommentService()
