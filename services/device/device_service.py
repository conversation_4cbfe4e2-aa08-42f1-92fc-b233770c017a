import hashlib
from datetime import datetime
from typing import List, Dict, Any

from core.logging_config import get_logger
from models.device import (
    DeviceInfo, DeviceVerificationRequest,
    DeviceVerificationResponse, DeveloperDeviceConfig
)
from config.settings import settings
from utils.custom_crypto import CustomCrypto

logger = get_logger(__name__)


class DeviceVerificationService:
    def __init__(self):
        self._developer_devices: List[DeveloperDeviceConfig] = []
        self._load_developer_devices()

    def _load_developer_devices(self):
        try:
            developer_fingerprints = settings.DEVELOPER_DEVICE_FINGERPRINTS

            if developer_fingerprints:
                for device_config in developer_fingerprints.split(','):
                    if ':' in device_config:
                        name, fingerprint = device_config.strip().split(':', 1)
                        self._developer_devices.append(
                            DeveloperDeviceConfig(
                                device_name=name.strip(),
                                device_fingerprint=fingerprint.strip(),
                                description=f"开发者设备 - {name.strip()}",
                                enabled=True
                            )
                        )

        except Exception:
            self._developer_devices = []
    
    def generate_device_fingerprint(self, device_info: DeviceInfo) -> str:
        try:
            fingerprint_components = [
                str(device_info.cpu_cores) if device_info.cpu_cores is not None else '',
                device_info.cpu_architecture if device_info.cpu_architecture is not None else '',
                (str(device_info.memory_size) if device_info.memory_size is not None else '').replace('.0', ''),
                device_info.gpu_vendor if device_info.gpu_vendor is not None else '',
                device_info.gpu_renderer if device_info.gpu_renderer is not None else '',
                str(device_info.screen_width) if device_info.screen_width is not None else '',
                str(device_info.screen_height) if device_info.screen_height is not None else '',
                str(device_info.screen_color_depth) if device_info.screen_color_depth is not None else '',
                (str(device_info.pixel_ratio) if device_info.pixel_ratio is not None else '').replace('.0', ''),
                device_info.platform if device_info.platform is not None else '',
                device_info.language if device_info.language is not None else '',
                device_info.timezone if device_info.timezone is not None else '',
                str(device_info.touch_support).lower() if device_info.touch_support is not None else '',
                str(device_info.cookie_enabled).lower() if device_info.cookie_enabled is not None else '',
                ''
            ]

            fingerprint_string = '|'.join(fingerprint_components)
            fingerprint_hash = hashlib.sha256(fingerprint_string.encode('utf-8')).hexdigest()

            return fingerprint_hash

        except Exception:
            return ""




    def _decrypt_device_info(self, encrypted_device_info: str) -> DeviceInfo:
        try:
            secret_key = 'simple-key-123'
            decrypted_data = CustomCrypto.decrypt(encrypted_device_info, secret_key)

            if not decrypted_data:
                return None

            components = decrypted_data.split('|')

            if len(components) != 14:
                return None

            device_info = DeviceInfo(
                cpu_cores=int(components[0]) if components[0] else None,
                cpu_architecture=components[1] if components[1] else None,
                memory_size=float(components[2]) if components[2] else None,
                gpu_vendor=components[3] if components[3] else None,
                gpu_renderer=components[4] if components[4] else None,
                screen_width=int(components[5]) if components[5] else None,
                screen_height=int(components[6]) if components[6] else None,
                screen_color_depth=int(components[7]) if components[7] else None,
                pixel_ratio=float(components[8]) if components[8] else None,
                platform=components[9] if components[9] else None,
                language=components[10] if components[10] else None,
                timezone=components[11] if components[11] else None,
                touch_support=components[12].lower() == 'true' if components[12] else None,
                cookie_enabled=components[13].lower() == 'true' if components[13] else None
            )

            return device_info

        except Exception:
            return None

    def verify_device(self, request: DeviceVerificationRequest) -> DeviceVerificationResponse:
        try:
            device_info = self._decrypt_device_info(request.encrypted_device_info)

            if not device_info:
                logger.warning("设备信息解密失败")
                return DeviceVerificationResponse(
                    is_developer=False,
                    device_fingerprint="",
                    message="设备信息解密失败"
                )

            device_fingerprint = self.generate_device_fingerprint(device_info)

            if not device_fingerprint:
                return DeviceVerificationResponse(
                    is_developer=False,
                    device_fingerprint="",
                    message="设备指纹生成失败"
                )

            is_developer = self._is_developer_device(device_fingerprint)

            if is_developer:
                self._update_device_last_used(device_fingerprint)

            return DeviceVerificationResponse(
                is_developer=is_developer,
                device_fingerprint=device_fingerprint,
                message="开发者设备验证通过" if is_developer else "非开发者设备"
            )

        except Exception:
            return DeviceVerificationResponse(
                is_developer=False,
                device_fingerprint="",
                message="设备验证异常"
            )
    
    def _is_developer_device(self, device_fingerprint: str) -> bool:
        """检查设备指纹是否匹配开发者设备"""
        for device_config in self._developer_devices:
            if (device_config.enabled and
                device_config.device_fingerprint == device_fingerprint):
                return True
        return False
    
    def _update_device_last_used(self, device_fingerprint: str):
        """更新设备最后使用时间"""
        for device_config in self._developer_devices:
            if device_config.device_fingerprint == device_fingerprint:
                device_config.last_used = datetime.now()
                break
    
    def get_developer_devices_count(self) -> int:
        """获取开发者设备数量（安全方法，不暴露具体设备信息）"""
        return len([d for d in self._developer_devices if d.enabled])
    
    def get_device_summary(self, device_info: DeviceInfo) -> Dict[str, Any]:
        """获取设备信息摘要"""
        return {
            "cpu_info": f"{device_info.cpu_cores or 0}核心 {device_info.cpu_architecture or ''}".strip(),
            "memory_info": f"{device_info.memory_size or 0}GB",
            "gpu_info": f"{device_info.gpu_vendor or ''} {device_info.gpu_renderer or ''}".strip(),
            "screen_info": f"{device_info.screen_width or 0}x{device_info.screen_height or 0}",
            "platform_info": device_info.platform or "未知",
            "timezone_info": device_info.timezone or "未知",
            "features": {
                "touch_support": device_info.touch_support or False,
                "cookie_enabled": device_info.cookie_enabled or False,
                "has_canvas_fingerprint": bool(device_info.canvas_fingerprint),
                "has_webgl_fingerprint": bool(device_info.webgl_fingerprint)
            }
        }


device_service = DeviceVerificationService()
