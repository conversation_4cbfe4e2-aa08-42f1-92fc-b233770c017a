import json
import re
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
from typing import Optional
from openai import OpenAI

from core.errors import ErrorCodes
from core.responses import success_response, error_response

from models.ai import (
    RealEstateAnalysisRequest, RealEstateAnalysisResult,
    BatchAnalysisResponse
)
from models.douyin import VideoInfo
from config.settings import settings
from database.ai import real_estate_db, VideoAnalysisResult


class RealEstateAnalysisService:
    def __init__(self):
        self.model_name = settings.AI_MODEL_NAME
        self.base_url = settings.AI_BASE_URL
        self.temperature = settings.AI_DEFAULT_TEMPERATURE
        self.max_tokens = settings.AI_DEFAULT_MAX_TOKENS
        self._load_prompts()

        try:
            real_estate_db.create_analysis_table_if_not_exists()
        except Exception:
            pass
    
    def _load_prompts(self):
        try:
            prompts_file = Path("docs/douyin/real_estate_analysis_prompts.md")
            if prompts_file.exists():
                with open(prompts_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                base_match = re.search(r'## 基础分析提示词\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.base_prompt = base_match.group(1).strip() if base_match else ""

                factory_match = re.search(r'## 厂房专项分析提示词\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.factory_prompt = factory_match.group(1).strip() if factory_match else ""
            else:
                self.base_prompt = ""
                self.factory_prompt = ""
        except Exception:
            self.base_prompt = ""
            self.factory_prompt = ""
    
    def _get_client(self) -> OpenAI:
        return OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url=self.base_url
        )

    def _build_analysis_prompt(self, video_description: str, use_factory_focus: bool = True) -> str:
        if not self.base_prompt:
            raise ValueError("提示词模板未加载")

        prompt = self.base_prompt
        if use_factory_focus and self.factory_prompt:
            prompt += f"\n\n{self.factory_prompt}"

        return f"{prompt}\n\n视频描述内容：{video_description}"
    def _analyze_video_worker(self, video: VideoInfo, timeout_seconds: int) -> Optional[RealEstateAnalysisResult]:
        """视频分析工作器 - 并行处理中的单个任务"""
        # 从statistics中提取评论数量
        comment_count = None
        if video.statistics and isinstance(video.statistics, dict):
            comment_count = video.statistics.get('comment_count', 0)

        description = video.desc
        if not description or (isinstance(description, str) and description.strip() in ['', 'None']):
            result = RealEstateAnalysisResult(
                aweme_id=video.aweme_id,
                video_description=description or '',
                is_real_estate_related=True,
                confidence_score=1.0,
                analysis_reasoning='视频描述为空，默认认为与厂房业务相关',
                keywords_found=[],
                category='厂房相关',
                intent_type='未知',
                create_time=getattr(video, 'create_time', None),
                comment_count=comment_count
            )

            db_result = VideoAnalysisResult(
                aweme_id=video.aweme_id,
                video_title_description=description or '',
                is_real_estate_related=True,
                confidence_score=1.0,
                analysis_result='视频描述为空，默认认为与厂房业务相关',
                keywords_found=json.dumps([], ensure_ascii=False),
                analysis_time=datetime.now()
            )
            real_estate_db.save_analysis_result(db_result)
            return result

        max_retries = 3
        base_delay = 1

        for attempt in range(max_retries):
            try:
                client = self._get_client()
                prompt = self._build_analysis_prompt(video.desc)

                completion = client.chat.completions.create(
                    model=self.model_name,
                    messages=[
                        {"role": "system", "content": "你是一名专业的房地产内容分析专家。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=self.temperature,
                    max_tokens=self.max_tokens,
                    timeout=timeout_seconds
                )

                response_content = completion.choices[0].message.content.strip()

                try:
                    result_data = json.loads(response_content)
                except json.JSONDecodeError:
                    json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                    if json_match:
                        result_data = json.loads(json_match.group())
                    else:
                        raise ValueError("解析失败")

                defaults = {
                    'is_real_estate_related': False,
                    'confidence_score': 0.0,
                    'analysis_reasoning': '分析失败',
                    'keywords_found': [],
                    'category': None,
                    'intent_type': None
                }
                for field, default_value in defaults.items():
                    if field not in result_data:
                        result_data[field] = default_value

                result_data['is_real_estate_related'] = bool(result_data.get('is_real_estate_related', False))
                result_data['confidence_score'] = float(result_data.get('confidence_score', 0.0))
                result_data['keywords_found'] = result_data.get('keywords_found', [])
                if isinstance(result_data['keywords_found'], str):
                    result_data['keywords_found'] = [result_data['keywords_found']]

                result = RealEstateAnalysisResult(
                    aweme_id=video.aweme_id,
                    video_description=video.desc,
                    is_real_estate_related=result_data['is_real_estate_related'],
                    confidence_score=result_data['confidence_score'],
                    analysis_reasoning=result_data['analysis_reasoning'],
                    keywords_found=result_data['keywords_found'],
                    category=result_data.get('category'),
                    intent_type=result_data.get('intent_type'),
                    create_time=getattr(video, 'create_time', None),
                    comment_count=comment_count
                )

                db_result = VideoAnalysisResult(
                    aweme_id=video.aweme_id,
                    video_title_description=video.desc,
                    is_real_estate_related=result.is_real_estate_related,
                    confidence_score=result.confidence_score,
                    analysis_result=result.analysis_reasoning,
                    keywords_found=json.dumps(result.keywords_found, ensure_ascii=False),
                    analysis_time=datetime.now()
                )
                real_estate_db.save_analysis_result(db_result)

                return result

            except Exception as e:
                error_msg = str(e).lower()
                if "rate limit" in error_msg or "too many requests" in error_msg:
                    delay = base_delay * (2 ** attempt)
                    time.sleep(delay)
                    continue
                elif attempt == max_retries - 1:
                    break
                else:
                    time.sleep(0.5)
                    continue

        return None

    def analyze_videos(self, request: RealEstateAnalysisRequest) -> dict:
        """分析视频列表 - 统一处理单个或多个视频"""
        start_time = time.time()

        try:
            videos = request.videos
            if not videos:
                return success_response(
                    data=BatchAnalysisResponse(
                        total_analyzed=0,
                        real_estate_count=0,
                        non_real_estate_count=0,
                        results=[],
                        processing_time=0.0,
                        success_rate=1.0
                    ).model_dump(),
                    message="没有视频数据需要分析"
                )

            # 并行处理所有视频
            results = []
            real_estate_count = 0

            with ThreadPoolExecutor(max_workers=min(150, len(videos))) as executor:
                futures = [
                    executor.submit(self._analyze_video_worker, video, request.timeout_seconds)
                    for video in videos
                ]

                for future in as_completed(futures, timeout=1800):
                    try:
                        result = future.result(timeout=5)
                        if result:
                            results.append(result)
                            if result.is_real_estate_related:
                                real_estate_count += 1
                    except Exception:
                        continue

            processing_time = time.time() - start_time
            success_count = len(results)
            success_rate = success_count / len(videos) if videos else 0.0

            return success_response(
                data=BatchAnalysisResponse(
                    total_analyzed=success_count,
                    real_estate_count=real_estate_count,
                    non_real_estate_count=success_count - real_estate_count,
                    results=results,
                    processing_time=processing_time,
                    success_rate=success_rate
                ).model_dump(),
                message=f"分析完成，共分析 {success_count}/{len(videos)} 个视频"
            )

        except Exception:
            return error_response(
                code=ErrorCodes.AI_SERVICE_ERROR,
                message="分析服务异常"
            )





real_estate_analysis_service = RealEstateAnalysisService()
