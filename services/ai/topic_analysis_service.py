import json
import re
import time
from pathlib import Path
from openai import OpenAI

from core.errors import ErrorCodes
from core.responses import success_response, error_response

from models.ai import (
    TopicAnalysisRequest, TopicAnalysisResponse, TopicResult
)
from config.settings import settings


class TopicAnalysisService:
    def __init__(self):
        self.model_name = settings.AI_MODEL_NAME
        self.base_url = settings.AI_BASE_URL
        self.temperature = settings.AI_DEFAULT_TEMPERATURE
        self.max_tokens = settings.AI_DEFAULT_MAX_TOKENS
        self._load_prompts()

    def _load_prompts(self):
        try:
            prompts_file = Path("docs/douyin/topic_extraction_prompts.md")
            if prompts_file.exists():
                with open(prompts_file, 'r', encoding='utf-8') as f:
                    content = f.read()

                base_match = re.search(r'### 通用需求分析\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.base_prompt = base_match.group(1).strip() if base_match else ""

                person_match = re.search(r'### 人物信息查询\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.person_info_prompt = person_match.group(1).strip() if person_match else ""

                service_match = re.search(r'### 服务需求分析\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.service_prompt = service_match.group(1).strip() if service_match else ""

                learning_match = re.search(r'### 学习需求分析\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.learning_prompt = learning_match.group(1).strip() if learning_match else ""

            else:
                self.base_prompt = ""
                self.person_info_prompt = ""
                self.service_prompt = ""
                self.learning_prompt = ""
        except Exception:
            self.base_prompt = ""
            self.person_info_prompt = ""
            self.service_prompt = ""
            self.learning_prompt = ""



    def _get_client(self) -> OpenAI:
        return OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url=self.base_url
        )
    
    def _build_prompt(self, text: str, analysis_type: str) -> str:
        if not self.base_prompt:
            raise ValueError("提示词模板未加载，请检查docs/topic_extraction_prompts.md文件")

        prompt = self.base_prompt

        if analysis_type == "person_info" and self.person_info_prompt:
            prompt += f"\n\n{self.person_info_prompt}"
        elif analysis_type == "service" and self.service_prompt:
            prompt += f"\n\n{self.service_prompt}"
        elif analysis_type == "learning" and self.learning_prompt:
            prompt += f"\n\n{self.learning_prompt}"
        elif analysis_type == "real_estate" and self.service_prompt:
            prompt += f"\n\n{self.service_prompt}"
        elif analysis_type == "business" and self.service_prompt:
            prompt += f"\n\n{self.service_prompt}"

        return f"{prompt}\n\n用户输入：{text}"
    
    def analyze_topic(self, request: TopicAnalysisRequest) -> dict:
        start_time = time.time()

        try:
            client = self._get_client()
            prompt = self._build_prompt(request.text, request.analysis_type)

            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一个专业的话题分析专家，擅长从文本中提取关键信息和话题。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                timeout=request.timeout_seconds
            )

            response_content = completion.choices[0].message.content.strip()

            try:
                result_data = json.loads(response_content)
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    result_data = json.loads(json_match.group())
                else:
                    raise ValueError("无法解析AI响应为JSON格式")

            topic_result = TopicResult(
                main_topic=result_data.get("main_topic", "未知话题"),
                sub_topics=result_data.get("sub_topics", []),
                keywords=result_data.get("keywords", []),
                entities=result_data.get("entities", {}),
                intent=result_data.get("intent"),
                confidence=float(result_data.get("confidence", 0.8)),
                category=result_data.get("category")
            )

            processing_time = time.time() - start_time

            response_data = TopicAnalysisResponse(
                original_text=request.text,
                result=topic_result,
                processing_time=processing_time,
                model_used=self.model_name
            )

            return success_response(
                data=response_data.model_dump(),
                message="话题分析完成"
            )
            
        except Exception:
            return error_response(
                code=ErrorCodes.AI_SERVICE_ERROR,
                message="话题分析服务暂时不可用，请稍后重试"
            )


topic_analysis_service = TopicAnalysisService()
