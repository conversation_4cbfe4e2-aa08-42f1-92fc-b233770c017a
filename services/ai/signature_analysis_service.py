import json
import re
import time
from pathlib import Path
from typing import Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from openai import OpenAI

from core.errors import ErrorCodes
from core.responses import success_response, error_response
from core.logging_config import get_logger
from models.ai.signature_analysis_models import SignatureAnalysisResult
from config.settings import settings
from services.ai.token_cost_calculator import token_cost_calculator
from database.ai import signature_analysis_db, task_cost_db, TaskCostRecord

logger = get_logger(__name__)


class SignatureAnalysisService:
    def __init__(self):
        self.model_name = settings.AI_MODEL_NAME
        self.base_url = settings.AI_BASE_URL
        self.temperature = settings.AI_DEFAULT_TEMPERATURE
        self.max_tokens = settings.AI_DEFAULT_MAX_TOKENS
        self.token_usage_records = []
        self._load_signature_prompt()

    def _load_signature_prompt(self):
        try:
            prompt_file = Path("docs/douyin/signature_analysis_prompts.md")
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                base_match = re.search(r'## 基础分析提示词\n\n```\n(.*?)\n```', content, re.DOTALL)
                self.signature_prompt = base_match.group(1).strip() if base_match else ""
            else:
                self.signature_prompt = ""
        except Exception:
            self.signature_prompt = ""

    def _get_client(self) -> OpenAI:
        return OpenAI(
            api_key=settings.DASHSCOPE_API_KEY,
            base_url=self.base_url
        )

    def _build_analysis_prompt(self, signature: str) -> str:
        if not self.signature_prompt:
            raise ValueError("提示词未加载")
        analysis_text = f"个性签名内容：\n{signature}"
        return f"{self.signature_prompt}\n\n{analysis_text}"

    def _analyze_single_signature(self, signature: str, timeout_seconds: int) -> Dict[str, Any]:
        try:
            client = self._get_client()
            prompt = self._build_analysis_prompt(signature)

            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "你是一名专业的用户身份识别专家，擅长从个性签名中识别销售人员和卖家。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.temperature,
                max_tokens=self.max_tokens,
                timeout=timeout_seconds
            )

            response_content = completion.choices[0].message.content.strip()

            if hasattr(completion, 'usage') and completion.usage:
                self.token_usage_records.append({
                    "model_name": self.model_name,
                    "input_tokens": completion.usage.prompt_tokens,
                    "output_tokens": completion.usage.completion_tokens
                })

            try:
                result_data = json.loads(response_content)
            except json.JSONDecodeError:
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    result_data = json.loads(json_match.group())
                else:
                    raise ValueError("响应解析失败")

            return result_data

        except Exception:
            return {
                "is_seller": False,
                "confidence": 0.0,
                "reasoning": "分析过程中出现错误",
                "seller_indicators": [],
                "category": None
            }

    def _analyze_signatures_batch(self, signatures: list) -> list:
        results = []
        with ThreadPoolExecutor(max_workers=10) as executor:
            future_to_signature = {
                executor.submit(self._analyze_single_signature, signature, 60): signature
                for signature in signatures
            }

            for future in as_completed(future_to_signature):
                try:
                    signature = future_to_signature[future]
                    analysis_result = future.result()

                    result = SignatureAnalysisResult(
                        signature=signature,
                        is_seller=analysis_result.get("is_seller", False),
                        confidence=float(analysis_result.get("confidence", 0.0)),
                        reasoning=analysis_result.get("reasoning", ""),
                        seller_indicators=analysis_result.get("seller_indicators", []),
                        category=analysis_result.get("category")
                    )
                    results.append(result)

                except Exception:
                    signature = future_to_signature[future]
                    error_result = SignatureAnalysisResult(
                        signature=signature,
                        is_seller=False,
                        confidence=0.0,
                        reasoning="分析失败",
                        seller_indicators=[],
                        category=None
                    )
                    results.append(error_result)

        return results

    def process_database_signatures(self, limit: int = 100) -> dict:
        start_time = time.time()
        self.token_usage_records = []

        try:
            pending_records = signature_analysis_db.get_pending_signatures(limit)

            if not pending_records:
                return success_response(
                    data={
                        "processed_count": 0,
                        "seller_count": 0,
                        "non_seller_count": 0,
                        "empty_signature_count": 0,
                        "processing_time": 0.0
                    },
                    message="没有待处理的签名数据"
                )

            empty_signature_records = []
            valid_signature_records = []

            for record in pending_records:
                if not record.signature or record.signature.strip() == "":
                    empty_signature_records.append(record)
                else:
                    valid_signature_records.append(record)

            empty_updates = []
            for record in empty_signature_records:
                empty_updates.append({
                    'id': record.id,
                    'status': '1'
                })

            if empty_updates:
                signature_analysis_db.batch_update_status(empty_updates)

            seller_count = 0
            non_seller_count = len(empty_signature_records)
            signature_updates = []

            if valid_signature_records:
                signatures = [record.signature for record in valid_signature_records]
                results = self._analyze_signatures_batch(signatures)

                for i, result in enumerate(results):
                    if i < len(valid_signature_records):
                        record = valid_signature_records[i]

                        if result.is_seller:
                            status = '2'
                            seller_count += 1
                        else:
                            status = '1'
                            non_seller_count += 1

                        signature_updates.append({
                            'id': record.id,
                            'status': status
                        })

                if signature_updates:
                    signature_analysis_db.batch_update_status(signature_updates)

            processing_time = time.time() - start_time
            total_processed = len(pending_records)

            token_cost_info = None
            if self.token_usage_records:
                token_cost_info = token_cost_calculator.calculate_batch_cost(self.token_usage_records)

                total_input_tokens = sum(r["input_tokens"] for r in self.token_usage_records)
                total_output_tokens = sum(r["output_tokens"] for r in self.token_usage_records)

                cost_record = TaskCostRecord(
                    task_type="signature_analysis_batch",
                    model_name=self.model_name,
                    input_tokens=total_input_tokens,
                    output_tokens=total_output_tokens,
                    total_tokens=token_cost_info["total_tokens"],
                    input_cost=token_cost_info["total_cost"] * 0.2,
                    output_cost=token_cost_info["total_cost"] * 0.8,
                    total_cost=token_cost_info["total_cost"],
                    task_data_count=len(valid_signature_records),
                    processing_time=processing_time
                )
                task_cost_db.save_task_cost(cost_record)

            result_data = {
                "processed_count": total_processed,
                "seller_count": seller_count,
                "non_seller_count": non_seller_count,
                "empty_signature_count": len(empty_signature_records),
                "processing_time": processing_time
            }

            if token_cost_info:
                result_data["token_cost_info"] = token_cost_info

            return success_response(
                data=result_data,
                message=f"签名分析处理完成，共处理{total_processed}条记录，识别出{seller_count}个厂房销售"
            )

        except Exception as e:
            logger.error(f"数据库签名处理失败: {e}")
            return error_response(
                code=ErrorCodes.AI_SERVICE_ERROR,
                message="数据库签名处理服务暂时不可用"
            )


signature_analysis_service = SignatureAnalysisService()
