"""
Token消耗和成本计算服务
"""

from typing import Dict, Any


class TokenCostCalculator:
    """Token成本计算器"""

    # 阿里云千锤百炼模型价格表（每千token的价格，单位：人民币元）
    MODEL_PRICING = {
        "qwen-max": {"input_price_per_1k": 0.0024, "output_price_per_1k": 0.0096},
        "qwen-plus": {"input_price_per_1k": 0.0008, "output_price_per_1k": 0.002},
        "qwen-turbo": {"input_price_per_1k": 0.0003, "output_price_per_1k": 0.0006},
        "default": {"input_price_per_1k": 0.001, "output_price_per_1k": 0.003}
    }

    def calculate_batch_cost(self, usage_records: list) -> Dict[str, Any]:
        """批量计算多次调用的总成本"""
        total_cost = 0.0
        total_tokens = 0
        model_costs = {}

        for record in usage_records:
            model_name = record.get("model_name", "default")
            input_tokens = record.get("input_tokens", 0)
            output_tokens = record.get("output_tokens", 0)

            pricing = self.MODEL_PRICING.get(model_name, self.MODEL_PRICING["default"])
            input_cost = (input_tokens / 1000) * pricing["input_price_per_1k"]
            output_cost = (output_tokens / 1000) * pricing["output_price_per_1k"]
            record_cost = input_cost + output_cost

            total_cost += record_cost
            total_tokens += input_tokens + output_tokens

            if model_name not in model_costs:
                model_costs[model_name] = {"total_cost": 0.0, "input_tokens": 0, "output_tokens": 0, "call_count": 0}

            model_costs[model_name]["total_cost"] += record_cost
            model_costs[model_name]["input_tokens"] += input_tokens
            model_costs[model_name]["output_tokens"] += output_tokens
            model_costs[model_name]["call_count"] += 1

        return {
            "total_cost": round(total_cost, 6),
            "total_tokens": total_tokens,
            "model_breakdown": model_costs,
            "call_count": len(usage_records)
        }


token_cost_calculator = TokenCostCalculator()
