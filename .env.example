# ReachRadar 环境配置示例文件
# 复制此文件为 .env 并根据实际情况修改配置

# 日志配置
LOG_LEVEL=INFO
LOG_STORAGE_PATH=logs

# 浏览器配置
BROWSER_WINDOW_SIZE=1920,1080

# AI服务配置
DASHSCOPE_API_KEY=your_dashscope_api_key_here
AI_MODEL_NAME=qwen-plus
AI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
AI_DEFAULT_TEMPERATURE=0.3
AI_DEFAULT_MAX_TOKENS=1000

# 抖音Cookie配置
DOUYIN_REFERENCE_COOKIES=your_reference_cookies_here

# 抖音官方OA配置
DOUYIN_OA_CLIENT_KEY=your_douyin_oa_client_key_here
DOUYIN_OA_CLIENT_SECRET=your_douyin_oa_client_secret_here
DOUYIN_OA_REDIRECT_URI=http://localhost:5173/auth/douyin/callback

# 延迟管理配置
SEARCH_DELAY_BASE=2.0
SEARCH_DELAY_MIN=1.2
SEARCH_DELAY_MAX=5.0
SEARCH_DELAY_STRATEGY=human_like
DELAY_DEBUG=false

# 开发者设备验证配置 - 格式: "设备名1:设备指纹1,设备名2:设备指纹2"
DEVELOPER_DEVICE_FINGERPRINTS=

# 代理配置
PROXY_API_URL=your_proxy_api_url_here
PROXY_USERNAME=your_proxy_username_here
PROXY_PASSWORD=your_proxy_password_here
PROXY_ENABLED=false

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_ENABLED=true
