from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class DouyinOAAccountDB(BaseModel):
    id: Optional[int] = None
    open_id: str
    union_id: Optional[str] = None
    nickname: str
    avatar: Optional[str] = None
    gender: Optional[int] = None
    city: Optional[str] = None
    province: Optional[str] = None
    country: Optional[str] = None
    access_token: str
    refresh_token: str
    expires_in: int
    refresh_expires_in: int
    scope: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    last_validation: Optional[datetime] = None
    is_valid: bool = True
    
    class Config:
        from_attributes = True



