import sqlite3
import json
from datetime import datetime
from typing import Optional
from pathlib import Path

from core.logging_config import get_logger
from database.douyin_official import DouyinOAAccountDB
from models.douyin_official import OATokenInfo, OAUserInfo

logger = get_logger(__name__)


class DouyinOADatabase:
    
    def __init__(self, db_path: str = "data/douyin_oa.db"):
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
    
    def _ensure_db_directory(self):
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def _init_database(self):
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            

            cursor.execute("""
                CREATE TABLE IF NOT EXISTS douyin_oa_accounts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    open_id TEXT UNIQUE NOT NULL,
                    union_id TEXT,
                    nickname TEXT NOT NULL,
                    avatar TEXT,
                    gender INTEGER,
                    city TEXT,
                    province TEXT,
                    country TEXT,
                    access_token TEXT NOT NULL,
                    refresh_token TEXT NOT NULL,
                    expires_in INTEGER NOT NULL,
                    refresh_expires_in INTEGER NOT NULL,
                    scope TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_validation TIMESTAMP,
                    is_valid BOOLEAN DEFAULT TRUE
                )
            """)
            conn.commit()
            logger.info("抖音OA数据库初始化完成")
    
    def save_account(self, token_info: OATokenInfo, user_info: OAUserInfo) -> DouyinOAAccountDB:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            scope_json = json.dumps(token_info.scope)
            now = datetime.now()

            # 转换HttpUrl类型为字符串
            avatar_str = str(user_info.avatar) if user_info.avatar else None

            cursor.execute("SELECT id FROM douyin_oa_accounts WHERE open_id = ?", (user_info.open_id,))
            existing = cursor.fetchone()
            
            if existing:

                cursor.execute("""
                    UPDATE douyin_oa_accounts SET
                        union_id = ?, nickname = ?, avatar = ?, gender = ?, city = ?, province = ?, country = ?,
                        access_token = ?, refresh_token = ?, expires_in = ?, refresh_expires_in = ?, scope = ?,
                        updated_at = ?, last_validation = ?, is_valid = TRUE
                    WHERE open_id = ?
                """, (
                    user_info.union_id, user_info.nickname, avatar_str, user_info.gender,
                    user_info.city, user_info.province, user_info.country,
                    token_info.access_token, token_info.refresh_token, token_info.expires_in,
                    token_info.refresh_expires_in, scope_json, now, now, user_info.open_id
                ))
            else:

                cursor.execute("""
                    INSERT INTO douyin_oa_accounts (
                        open_id, union_id, nickname, avatar, gender, city, province, country,
                        access_token, refresh_token, expires_in, refresh_expires_in, scope,
                        created_at, updated_at, last_validation, is_valid
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, TRUE)
                """, (
                    user_info.open_id, user_info.union_id, user_info.nickname, avatar_str,
                    user_info.gender, user_info.city, user_info.province, user_info.country,
                    token_info.access_token, token_info.refresh_token, token_info.expires_in,
                    token_info.refresh_expires_in, scope_json, now, now, now
                ))
            
            conn.commit()
            

            return self.get_account_by_open_id(user_info.open_id)
    
    def get_account_by_open_id(self, open_id: str) -> Optional[DouyinOAAccountDB]:
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM douyin_oa_accounts WHERE open_id = ?", (open_id,))
            row = cursor.fetchone()
            
            if row:
                return DouyinOAAccountDB(**dict(row))
            return None
    

    def update_token(self, open_id: str, token_info: OATokenInfo) -> bool:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            scope_json = json.dumps(token_info.scope)
            now = datetime.now()
            
            cursor.execute("""
                UPDATE douyin_oa_accounts SET
                    access_token = ?, refresh_token = ?, expires_in = ?, refresh_expires_in = ?, 
                    scope = ?, updated_at = ?, last_validation = ?, is_valid = TRUE
                WHERE open_id = ?
            """, (
                token_info.access_token, token_info.refresh_token, token_info.expires_in,
                token_info.refresh_expires_in, scope_json, now, now, open_id
            ))
            
            conn.commit()
            return cursor.rowcount > 0
    
    def update_validation_status(self, open_id: str, is_valid: bool) -> bool:
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute("""
                UPDATE douyin_oa_accounts SET
                    is_valid = ?, last_validation = ?
                WHERE open_id = ?
            """, (is_valid, datetime.now(), open_id))
            
            conn.commit()
            return cursor.rowcount > 0
    



douyin_oa_db = DouyinOADatabase()
