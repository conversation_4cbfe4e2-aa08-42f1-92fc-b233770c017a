import pymysql
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, Any, List

from config.settings import settings
from database.douyin.models import DouyinVideoDataComplete


class DouyinVideoDatabase:

    def __init__(self):
        self.connection_config = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': True
        }
        self._test_connection()
        self.create_table_if_not_exists()

    def _test_connection(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
        except Exception:
            raise

    @contextmanager
    def _get_connection(self):
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception:
            raise
        finally:
            if connection:
                connection.close()
    


    def create_table_if_not_exists(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    create_sql = """
                        CREATE TABLE IF NOT EXISTS douyin_video_data (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            search_topic VARCHAR(255) NOT NULL COMMENT '搜索话题',
                            video_id BIGINT UNIQUE NOT NULL COMMENT '视频ID',
                            video_title_description TEXT COMMENT '视频标题/描述',
                            author_nickname VARCHAR(255) COMMENT '作者昵称',
                            author_douyin_id VARCHAR(255) COMMENT '作者抖音号',
                            like_count BIGINT DEFAULT 0 COMMENT '点赞数',
                            comment_count BIGINT DEFAULT 0 COMMENT '评论数',
                            share_count BIGINT DEFAULT 0 COMMENT '分享数',
                            play_count BIGINT DEFAULT 0 COMMENT '播放数',
                            publish_time DATETIME COMMENT '发布时间',
                            video_link VARCHAR(500) COMMENT '视频链接',
                            is_related VARCHAR(10) DEFAULT '0' COMMENT '是否相关',
                            create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            INDEX idx_search_topic (search_topic),
                            INDEX idx_video_id (video_id),
                            INDEX idx_is_related (is_related),
                            INDEX idx_publish_time (publish_time),
                            INDEX idx_create_time (create_time)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='抖音视频数据表'
                    """
                    cursor.execute(create_sql)
        except Exception:
            raise

    def batch_insert_videos(self, search_keyword: str, videos: List[Dict[str, Any]]) -> dict:
        if not search_keyword or not search_keyword.strip():
            raise ValueError("参数错误")

        if not videos:
            return {
                "inserted_count": 0,
                "skipped_count": 0,
                "error_count": 0
            }

        inserted_count = 0
        skipped_count = 0
        error_count = 0
        current_time = datetime.now()

        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    for video in videos:
                        try:
                            if not video.get('aweme_id'):
                                error_count += 1
                                continue

                            db_video = self._convert_video_info_to_db_model(search_keyword, video, current_time)
                            sql = """
                                INSERT IGNORE INTO douyin_video_data (
                                    search_topic, video_id, video_title_description, author_nickname,
                                    author_douyin_id, like_count, comment_count, share_count, play_count,
                                    publish_time, video_link, is_related, create_time
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                )
                            """
                            cursor.execute(sql, (
                                db_video.search_topic, db_video.video_id, db_video.video_title_description,
                                db_video.author_nickname, db_video.author_douyin_id, db_video.like_count,
                                db_video.comment_count, db_video.share_count, db_video.play_count,
                                db_video.publish_time, db_video.video_link, db_video.is_related,
                                db_video.create_time
                            ))
                            if cursor.rowcount > 0:
                                inserted_count += 1
                            else:
                                skipped_count += 1
                        except (pymysql.Error, Exception):
                            error_count += 1
                            continue
                    return {
                        "inserted_count": inserted_count,
                        "skipped_count": skipped_count,
                        "error_count": error_count
                    }

        except Exception:
            raise

    def _convert_video_info_to_db_model(self, search_keyword: str, video: Dict[str, Any], current_time: datetime) -> DouyinVideoDataComplete:
        statistics = video.get('statistics', {}) or {}
        publish_time = None
        if video.get('create_time'):
            try:
                publish_time = datetime.fromtimestamp(video['create_time'])
            except (ValueError, TypeError):
                publish_time = None

        video_link = video.get('douyin_link')
        if not video_link and video.get('aweme_id'):
            video_link = f"https://www.douyin.com/video/{video['aweme_id']}"

        return DouyinVideoDataComplete(
            search_topic=search_keyword,
            video_id=str(video.get('aweme_id', '')),
            video_title_description=video.get('desc', ''),
            author_nickname=video.get('author_nickname', ''),
            author_douyin_id=video.get('author_unique_id', ''),
            like_count=statistics.get('digg_count', 0) or 0,
            comment_count=statistics.get('comment_count', 0) or 0,
            share_count=statistics.get('share_count', 0) or 0,
            play_count=statistics.get('play_count', 0) or 0,
            publish_time=publish_time,
            video_link=video_link,
            is_related="0",
            create_time=current_time
        )
    

douyin_video_db = DouyinVideoDatabase()
