import pymysql
from contextlib import contextmanager
from datetime import datetime
from typing import List, Union

from config.settings import settings
from database.ai import CustomerAnalysisResult


class CustomerAnalysisDatabase:

    def __init__(self):
        self.connection_config = {
            'host': settings.MYSQL_HOST,
            'port': settings.MYSQL_PORT,
            'user': settings.MYSQL_USER,
            'password': settings.MYSQL_PASSWORD,
            'database': settings.MYSQL_DATABASE,
            'charset': settings.MYSQL_CHARSET,
            'autocommit': True
        }
        self._test_connection()
        self.create_table_if_not_exists()

    def _test_connection(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
        except Exception:
            raise

    @contextmanager
    def _get_connection(self):
        connection = None
        try:
            connection = pymysql.connect(**self.connection_config)
            yield connection
        except Exception:
            raise
        finally:
            if connection:
                connection.close()

    def create_table_if_not_exists(self):
        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    create_sql = """
                        CREATE TABLE IF NOT EXISTS customer_analysis_results (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            user_nickname VARCHAR(255) COMMENT '用户昵称',
                            douyin_id VARCHAR(100) COMMENT '抖音号',
                            sec_uid VARCHAR(255) COMMENT '用户sec_uid',
                            user_id VARCHAR(100) COMMENT '用户ID',
                            intent_type VARCHAR(50) COMMENT '意向类型',
                            confidence DECIMAL(5,4) NOT NULL COMMENT '置信度',
                            analysis_reason TEXT COMMENT '分析原因',
                            comment_content TEXT COMMENT '评论内容（JSON格式）',
                            comment_count INT COMMENT '评论数量',
                            comment_times TEXT COMMENT '评论时间（JSON格式）',
                            ip_addresses TEXT COMMENT 'IP地址（JSON格式）',
                            latest_comment_time VARCHAR(50) COMMENT '最新评论时间',
                            related_video_links TEXT COMMENT '相关视频链接（JSON格式）',
                            import_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '导入时间',
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            UNIQUE KEY uk_douyin_id (douyin_id),
                            UNIQUE KEY uk_user_id (user_id),
                            INDEX idx_intent_type (intent_type),
                            INDEX idx_confidence (confidence),
                            INDEX idx_created_at (created_at)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户分析结果表'
                    """
                    cursor.execute(create_sql)
        except Exception:
            raise
    
    def save_customers(self, records: Union[CustomerAnalysisResult, List[CustomerAnalysisResult]],
                      batch_size: int = 1000) -> dict:
        if isinstance(records, CustomerAnalysisResult):
            records = [records]

        if not records:
            return {"success_count": 0, "skipped_count": 0, "failed_count": 0, "total_count": 0}

        total_results = {
            "success_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "failed_users": [],
            "total_count": len(records)
        }

        try:
            for batch_start in range(0, len(records), batch_size):
                batch_end = min(batch_start + batch_size, len(records))
                batch_records = records[batch_start:batch_end]

                batch_result = self._batch_insert(batch_records)

                total_results["success_count"] += batch_result["success_count"]
                total_results["skipped_count"] += batch_result["skipped_count"]
                total_results["failed_count"] += batch_result["failed_count"]
                total_results["failed_users"].extend(batch_result["failed_users"])

            return total_results

        except Exception:
            return {
                "success_count": 0,
                "skipped_count": 0,
                "failed_count": len(records),
                "failed_users": [r.user_nickname or "未知用户" for r in records],
                "total_count": len(records)
            }

    def _batch_insert(self, records: List[CustomerAnalysisResult]) -> dict:
        if not records:
            return {"success_count": 0, "skipped_count": 0, "failed_count": 0, "failed_users": []}

        try:
            with self._get_connection() as conn:
                with conn.cursor() as cursor:
                    conn.begin()

                    insert_sql = """
                        INSERT IGNORE INTO customer_analysis_results (
                            user_nickname, douyin_id, sec_uid, user_id, intent_type,
                            confidence, analysis_reason, comment_content, comment_count,
                            comment_times, ip_addresses, latest_comment_time,
                            related_video_links, import_time
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """

                    batch_data = []
                    for record in records:
                        batch_data.append((
                            record.user_nickname, record.douyin_id, record.sec_uid,
                            record.user_id, record.intent_type, record.confidence,
                            record.analysis_reason, record.comment_content, record.comment_count,
                            record.comment_times, record.ip_addresses, record.latest_comment_time,
                            record.related_video_links, record.import_time or datetime.now()
                        ))

                    cursor.executemany(insert_sql, batch_data)
                    success_count = cursor.rowcount
                    skipped_count = len(records) - success_count

                    conn.commit()

                    return {
                        "success_count": success_count,
                        "skipped_count": skipped_count,
                        "failed_count": 0,
                        "failed_users": []
                    }

        except Exception:
            return {
                "success_count": 0,
                "skipped_count": 0,
                "failed_count": len(records),
                "failed_users": [r.user_nickname or "未知用户" for r in records]
            }
    


    def save_customer_analysis_result(self, record: CustomerAnalysisResult) -> bool:
        result = self.save_customers(record)
        return result["success_count"] > 0

    def batch_save_customer_analysis_results(self, records: List[CustomerAnalysisResult]) -> dict:
        return self.save_customers(records)


customer_analysis_db = CustomerAnalysisDatabase()
