#!/bin/bash

# ReachRadar 启动脚本 - 支持前台和后台运行

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
CYAN='\033[0;36m'
NC='\033[0m'

# 进程管理 - 不使用PID文件

# 显示帮助信息
show_help() {
    echo "ReachRadar 启动脚本"
    echo ""
    echo "用法:"
    echo "  $0                    # 前台启动前后端服务"
    echo "  $0 -d, --daemon      # 后台启动前后端服务"
    echo "  $0 backend           # 仅前台启动后端"
    echo "  $0 backend -d        # 仅后台启动后端"
    echo "  $0 frontend          # 仅前台启动前端"
    echo "  $0 frontend -d       # 仅后台启动前端"
    echo "  $0 stop              # 停止所有后台服务"
    echo "  $0 status            # 查看服务状态"
    echo "  $0 logs              # 查看系统日志"
    echo "  $0 restart           # 重启所有服务"
    echo "  $0 -h, --help        # 显示此帮助信息"
    echo ""
    echo "后台模式说明:"
    echo "  - 服务将在后台运行，不占用终端"
    echo "  - 后端日志输出到 logs/ 目录"
    echo "  - 使用 '$0 stop' 停止服务"
    echo "  - 使用 '$0 status' 查看运行状态"
}

# 检查服务状态
check_status() {
    echo -e "${BLUE}=== 服务状态检查 ===${NC}"

    # 检查后端 - 通过进程名查找
    BACKEND_PID=$(pgrep -f "uvicorn main:app.*--port 8004" 2>/dev/null)
    if [[ -n "$BACKEND_PID" ]]; then
        echo -e "${GREEN}✓ 后端服务运行中 (PID: $BACKEND_PID, 端口: 8004)${NC}"
    else
        echo -e "${RED}✗ 后端服务未运行${NC}"
    fi

    # 检查前端 - 通过端口检查更准确
    if lsof -i :5173 >/dev/null 2>&1; then
        FRONTEND_PID=$(lsof -t -i :5173 2>/dev/null | head -1)
        echo -e "${GREEN}✓ 前端服务运行中 (PID: $FRONTEND_PID, 端口: 5173)${NC}"
    else
        echo -e "${RED}✗ 前端服务未运行${NC}"
    fi
}

# 停止服务
stop_services() {
    echo -e "${YELLOW}正在停止服务...${NC}"

    # 停止后端 - 通过进程名查找并停止
    BACKEND_PIDS=$(pgrep -f "uvicorn main:app.*--port 8004" 2>/dev/null)
    if [[ -n "$BACKEND_PIDS" ]]; then
        echo "停止后端服务..."
        for pid in $BACKEND_PIDS; do
            kill "$pid" 2>/dev/null
        done
        sleep 2
        # 检查是否还有残留进程
        BACKEND_PIDS=$(pgrep -f "uvicorn main:app.*--port 8004" 2>/dev/null)
        if [[ -n "$BACKEND_PIDS" ]]; then
            echo "强制停止后端服务..."
            for pid in $BACKEND_PIDS; do
                kill -9 "$pid" 2>/dev/null
            done
        fi
    fi

    # 停止前端 - 通过端口查找并停止
    FRONTEND_PIDS=$(lsof -t -i :5173 2>/dev/null)
    if [[ -n "$FRONTEND_PIDS" ]]; then
        echo "停止前端服务..."
        for pid in $FRONTEND_PIDS; do
            kill "$pid" 2>/dev/null
        done
        sleep 2
        # 检查是否还有残留进程
        FRONTEND_PIDS=$(lsof -t -i :5173 2>/dev/null)
        if [[ -n "$FRONTEND_PIDS" ]]; then
            echo "强制停止前端服务..."
            for pid in $FRONTEND_PIDS; do
                kill -9 "$pid" 2>/dev/null
            done
        fi
    fi

    echo -e "${GREEN}服务已停止${NC}"
}

# 查看日志
show_logs() {
    echo -e "${CYAN}=== 服务日志 ===${NC}"
    echo -e "${YELLOW}后端日志 (logs/):${NC}"
    if [[ -d "logs" ]]; then
        echo "最新的访问日志:"
        tail -10 logs/access.log 2>/dev/null || echo "访问日志为空"
        echo ""
        echo "最新的错误日志:"
        tail -10 logs/error.log 2>/dev/null || echo "错误日志为空"
        echo ""
        echo "最新的信息日志:"
        tail -10 logs/info.log 2>/dev/null || echo "信息日志为空"
    else
        echo "日志目录不存在"
    fi

    echo ""
    echo -e "${YELLOW}前端开发服务器日志:${NC}"
    echo "前端使用 Vite 开发服务器，日志直接输出到终端"
}

# 解析参数
DAEMON_MODE=false
SERVICE_TYPE="all"

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--daemon)
            DAEMON_MODE=true
            shift
            ;;
        backend)
            SERVICE_TYPE="backend"
            shift
            ;;
        frontend)
            SERVICE_TYPE="frontend"
            shift
            ;;
        stop)
            stop_services
            exit 0
            ;;
        status)
            check_status
            exit 0
            ;;
        logs)
            show_logs
            exit 0
            ;;
        restart)
            stop_services
            sleep 2
            # 重启时默认使用后台模式
            DAEMON_MODE=true
            SERVICE_TYPE="all"
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

echo -e "${BLUE}=== ReachRadar 服务器启动 ===${NC}"
if [[ "$DAEMON_MODE" == true ]]; then
    echo -e "${CYAN}运行模式: 后台模式${NC}"
else
    echo -e "${CYAN}运行模式: 前台模式${NC}"
fi

# 检查环境
check_environment() {
    echo -e "${BLUE}检查环境...${NC}"

    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: Python3 未安装${NC}"
        exit 1
    fi

    if [[ "$SERVICE_TYPE" != "backend" ]] && ! command -v node &> /dev/null; then
        echo -e "${RED}错误: Node.js 未安装${NC}"
        exit 1
    fi

    # 检查前端依赖
    if [[ "$SERVICE_TYPE" != "backend" ]] && [ ! -d "frontend/node_modules" ]; then
        echo -e "${YELLOW}安装前端依赖...${NC}"
        cd frontend && npm install && cd ..
    fi

    echo -e "${GREEN}环境检查完成${NC}"
}

# 启动后端服务
start_backend() {
    # 检查是否已经在运行
    EXISTING_PID=$(pgrep -f "uvicorn main:app.*--port 8004" 2>/dev/null)
    if [[ -n "$EXISTING_PID" ]]; then
        echo -e "${YELLOW}后端服务已在运行 (PID: $EXISTING_PID)${NC}"
        return 0
    fi

    echo -e "${BLUE}启动后端服务...${NC}"

    # 确保在项目根目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    cd "$SCRIPT_DIR"

    # 检查 .env 文件是否存在
    if [[ ! -f ".env" ]]; then
        echo -e "${YELLOW}警告: .env 文件不存在，将使用默认配置${NC}"
    else
        echo -e "${GREEN}✓ 找到 .env 配置文件${NC}"
    fi

    if [[ "$DAEMON_MODE" == true ]]; then
        # 后台模式 - 使用系统日志，不重定向输出
        nohup python3 -m uvicorn main:app --host 0.0.0.0 --port 8004 --reload >/dev/null 2>&1 &
        BACKEND_PID=$!
        echo -e "${GREEN}✓ 后端服务已启动 (PID: $BACKEND_PID, 日志: logs/)${NC}"
    else
        # 前台模式
        python3 -m uvicorn main:app --host 0.0.0.0 --port 8004 --reload &
        BACKEND_PID=$!
    fi
}

# 启动前端服务
start_frontend() {
    # 检查是否已经在运行
    if lsof -i :5173 >/dev/null 2>&1; then
        EXISTING_PID=$(lsof -t -i :5173 2>/dev/null | head -1)
        echo -e "${YELLOW}前端服务已在运行 (PID: $EXISTING_PID)${NC}"
        return 0
    fi

    echo -e "${BLUE}启动前端服务...${NC}"

    if [[ "$DAEMON_MODE" == true ]]; then
        # 后台模式 - 前端开发服务器不需要额外日志
        cd frontend
        nohup npm run dev >/dev/null 2>&1 &
        FRONTEND_PID=$!
        cd ..
        echo -e "${GREEN}✓ 前端服务已启动 (PID: $FRONTEND_PID)${NC}"
    else
        # 前台模式
        cd frontend && npm run dev &
        FRONTEND_PID=$!
        cd ..
    fi
}

# 前台模式的清理函数
cleanup_foreground() {
    echo -e "\n${YELLOW}正在停止服务...${NC}"
    if [[ -n "$BACKEND_PID" ]]; then
        kill $BACKEND_PID 2>/dev/null
    fi
    if [[ -n "$FRONTEND_PID" ]]; then
        kill $FRONTEND_PID 2>/dev/null
    fi
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}服务已停止${NC}"
    exit 0
}

# 检查环境
check_environment

# 根据服务类型启动服务
if [[ "$SERVICE_TYPE" == "backend" ]]; then
    start_backend
elif [[ "$SERVICE_TYPE" == "frontend" ]]; then
    start_frontend
else
    # 启动所有服务
    start_backend
    start_frontend
fi

# 显示访问信息
echo ""
echo -e "${GREEN}服务启动完成！${NC}"
echo -e "${YELLOW}访问地址:${NC}"
echo "  前端: http://localhost:5173"
echo "  后端: http://localhost:8004"
echo "  API文档: http://localhost:8004/docs"

if [[ "$DAEMON_MODE" == true ]]; then
    echo ""
    echo -e "${CYAN}后台模式命令:${NC}"
    echo "  查看状态: $0 status"
    echo "  查看日志: $0 logs"
    echo "  停止服务: $0 stop"
    echo "  重启服务: $0 restart"
else
    echo ""
    echo -e "${YELLOW}按 Ctrl+C 停止服务${NC}"

    # 注册信号处理
    trap cleanup_foreground SIGINT SIGTERM

    # 等待进程
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
fi
