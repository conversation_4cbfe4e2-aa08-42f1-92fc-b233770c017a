import json
import requests
from typing import Dict, Any, List, Optional
from urllib.parse import quote
from core.logging_config import get_logger

from models.douyin import CookieData
from config.settings import DouyinConstants
from core.errors import ErrorCodes
from core.responses import error_response


class DouyinHttpClient:
    def __init__(self):
        self.default_timeout = 10
        self.logger = get_logger(__name__)
    
    def get(self,
            url: str,
            cookies: Optional[List[CookieData]] = None,
            params: Optional[Dict[str, Any]] = None,
            referer: Optional[str] = None,
            timeout: Optional[int] = None) -> dict:
        return self._make_request(
            method='GET',
            url=url,
            cookies=cookies,
            params=params,
            referer=referer,
            timeout=timeout
        )
    
    def post(self,
             url: str,
             cookies: Optional[List[CookieData]] = None,
             data: Optional[Dict[str, Any]] = None,
             json_data: Optional[Dict[str, Any]] = None,
             referer: Optional[str] = None,
             timeout: Optional[int] = None,
             extra_headers: Optional[Dict[str, str]] = None) -> dict:
        return self._make_request(
            method='POST',
            url=url,
            cookies=cookies,
            data=data,
            json_data=json_data,
            referer=referer,
            timeout=timeout,
            extra_headers=extra_headers
        )
    
    def _make_request(self,
                     method: str,
                     url: str,
                     cookies: Optional[List[CookieData]] = None,
                     params: Optional[Dict[str, Any]] = None,
                     data: Optional[Dict[str, Any]] = None,
                     json_data: Optional[Dict[str, Any]] = None,
                     referer: Optional[str] = None,
                     timeout: Optional[int] = None,
                     extra_headers: Optional[Dict[str, str]] = None) -> dict:
        return self._make_http_request(
            method=method,
            url=url,
            cookies=cookies,
            params=params,
            data=data,
            json_data=json_data,
            referer=referer,
            timeout=timeout,
            extra_headers=extra_headers
        )

    def _make_http_request(self,
                          method: str,
                          url: str,
                          cookies: Optional[List[CookieData]] = None,
                          params: Optional[Dict[str, Any]] = None,
                          data: Optional[Dict[str, Any]] = None,
                          json_data: Optional[Dict[str, Any]] = None,
                          referer: Optional[str] = None,
                          timeout: Optional[int] = None,
                          extra_headers: Optional[Dict[str, str]] = None) -> dict:
        try:
            cookie_string = '; '.join([f"{cookie.name}={cookie.value}" for cookie in cookies]) if cookies else ""
            headers = self._build_headers(cookie_string, referer)

            # 添加额外的headers
            if extra_headers:
                headers.update(extra_headers)

            if method.upper() == 'GET':
                response = requests.get(
                    url,
                    params=params,
                    headers=headers,
                    timeout=timeout or self.default_timeout,
                    allow_redirects=False,
                    stream=False
                )
            else:
                response = requests.post(
                    url,
                    data=data,
                    json=json_data,
                    headers=headers,
                    timeout=timeout or self.default_timeout,
                    allow_redirects=False
                )

            if response.status_code != 200:
                return error_response(
                    code=ErrorCodes.DOUYIN_API_ERROR,
                    message="请求失败，请稍后重试"
                )

            return self._parse_standard_response(response)

        except requests.exceptions.Timeout:
            return error_response(
                code=ErrorCodes.REQUEST_TIMEOUT,
                message="请求超时，请稍后重试"
            )
        except requests.exceptions.RequestException:
            return error_response(
                code=ErrorCodes.DOUYIN_API_ERROR,
                message="网络请求失败，请检查网络连接"
            )
        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_API_ERROR,
                message="请求处理异常"
            )

    def _build_headers(self, cookie_string: str, referer: Optional[str] = None) -> Dict[str, str]:
        headers = {
            'User-Agent': DouyinConstants.get_random_user_agent(),
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin'
        }

        if cookie_string:
            headers['Cookie'] = cookie_string
        
        if referer:
            headers['Referer'] = referer
        else:
            headers['Referer'] = 'https://www.douyin.com/'
        
        return headers
    
    def _parse_standard_response(self, response: requests.Response) -> dict:
        try:
            if response.encoding is None:
                response.encoding = 'utf-8'

            response_text = response.text.strip()

            if not response_text:
                return error_response(
                    code=ErrorCodes.DOUYIN_AUTH_FAILED,
                    message="登录状态已失效，请重新登录"
                )

            json_start = response_text.find('{')
            if json_start > 0:
                response_text = response_text[json_start:]

            response_data = json.loads(response_text)
            return {
                'success': True,
                'data': response_data
            }

        except json.JSONDecodeError:
            return error_response(
                code=ErrorCodes.DOUYIN_API_ERROR,
                message="响应数据格式错误"
            )
        except Exception:
            return error_response(
                code=ErrorCodes.DOUYIN_API_ERROR,
                message="响应解析失败"
            )


    
    def build_referer_with_keyword(self, base_referer: str, keyword: str, aid: Optional[str] = None) -> str:
        encoded_keyword = quote(keyword, safe='')
        referer_url = f"{base_referer}/{encoded_keyword}"
        if aid:
            referer_url += f"?aid={aid}&type=general"

        return referer_url

douyin_http_client = DouyinHttpClient()
