"""代理验证器"""

import asyncio
import time
from typing import List
import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from config.settings import settings
from core.logging_config import get_logger


class ProxyValidator:
    """代理验证器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.timeout = settings.PROXY_VALIDATION_TIMEOUT
        self.max_response_time = settings.PROXY_MAX_RESPONSE_TIME
        
        # 测试URL
        self.test_url = "http://httpbin.org/ip"

    def validate_single_proxy(self, proxy_ip_port: str) -> bool:
        """验证单个代理的有效性"""
        proxy_url = f"http://{settings.PROXY_USERNAME}:{settings.PROXY_PASSWORD}@{proxy_ip_port}"
        proxy_config = {
            'http': proxy_url,
            'https': proxy_url
        }

        try:
            start_time = time.time()
            response = requests.get(
                self.test_url,
                proxies=proxy_config,
                timeout=self.timeout,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )

            response_time = time.time() - start_time
            return response.status_code == 200 and response_time <= self.max_response_time

        except Exception:
            return False
    
    def validate_proxy_list(self, proxy_list: List[str], max_workers: int = 10) -> List[str]:
        """批量验证代理列表"""
        if not proxy_list:
            return []

        self.logger.info(f"开始验证 {len(proxy_list)} 个代理")
        valid_proxies = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_proxy = {
                executor.submit(self.validate_single_proxy, proxy): proxy
                for proxy in proxy_list
            }

            for future in as_completed(future_to_proxy):
                proxy = future_to_proxy[future]
                try:
                    if future.result():
                        valid_proxies.append(proxy)
                except Exception as e:
                    self.logger.error(f"验证代理 {proxy} 异常: {e}")

        self.logger.info(f"验证完成: {len(valid_proxies)}/{len(proxy_list)} 个代理有效")
        return valid_proxies
    

    async def async_validate_proxy_list(self, proxy_list: List[str], max_workers: int = 10) -> List[str]:
        """异步批量验证代理列表"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.validate_proxy_list, proxy_list, max_workers)


# 全局代理验证器实例
proxy_validator = ProxyValidator()
