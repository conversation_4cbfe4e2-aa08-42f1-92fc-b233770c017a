"""
自定义加密工具类
"""


class CustomCrypto:
    
    @staticmethod
    def encrypt(data: str, key: str) -> str:
        """简单加密"""
        if not data or not key:
            return ''

        try:
            result = ''
            data_bytes = data.encode('utf-8')

            for i, data_byte in enumerate(data_bytes):
                key_byte = ord(key[i % len(key)])
                encrypted = data_byte ^ key_byte
                result += f"{encrypted:02x}"

            return result
        except:
            return ''

    @staticmethod
    def decrypt(encrypted_data: str, key: str) -> str:
        """简单解密"""
        if not encrypted_data or not key or len(encrypted_data) % 2 != 0:
            return ''

        try:
            bytes_list = []

            for i in range(0, len(encrypted_data), 2):
                encrypted_byte = int(encrypted_data[i:i+2], 16)
                key_byte = ord(key[(i // 2) % len(key)])
                decrypted = encrypted_byte ^ key_byte
                bytes_list.append(decrypted)

            byte_array = bytes(bytes_list)
            return byte_array.decode('utf-8')
        except:
            return ''
