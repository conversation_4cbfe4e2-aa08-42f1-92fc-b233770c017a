import time
import random
import os
from typing import Optional
from enum import Enum
import numpy as np



class DelayStrategy(Enum):
    """延迟策略枚举"""
    UNIFORM = "uniform"          # 均匀分布
    NORMAL = "normal"            # 正态分布  
    EXPONENTIAL = "exponential"  # 指数分布
    PROGRESSIVE = "progressive"  # 渐进式延迟
    HUMAN_LIKE = "human_like"    # 模拟人类行为


class DelayManager:
    """随机延迟管理器 - 用于防止API调用过于频繁被检测"""
    
    def __init__(self, 
                 base_delay: float = 1.0,
                 min_delay: float = 0.5,
                 max_delay: float = 3.0,
                 strategy: DelayStrategy = DelayStrategy.HUMAN_LIKE,
                 debug_mode: bool = False):
        """
        初始化延迟管理器
        
        Args:
            base_delay: 基础延迟时间（秒）
            min_delay: 最小延迟时间（秒）
            max_delay: 最大延迟时间（秒）
            strategy: 延迟策略
            debug_mode: 调试模式（快速测试，延迟时间缩短）
        """
        self.base_delay = base_delay
        self.min_delay = min_delay
        self.max_delay = max_delay
        self.strategy = strategy
        self.debug_mode = debug_mode
        
        # 渐进式延迟相关
        self.request_count = 0
        self.progressive_factor = 1.0
        
        # 从环境变量读取配置
        self._load_config_from_env()
        
        # 调试模式下缩短延迟时间
        if self.debug_mode:
            self.base_delay *= 0.1
            self.min_delay *= 0.1
            self.max_delay *= 0.1
    
    def _load_config_from_env(self):
        """从环境变量加载配置"""
        try:
            self.base_delay = float(os.getenv('DELAY_BASE', self.base_delay))
            self.min_delay = float(os.getenv('DELAY_MIN', self.min_delay))
            self.max_delay = float(os.getenv('DELAY_MAX', self.max_delay))
            
            strategy_str = os.getenv('DELAY_STRATEGY', self.strategy.value)
            try:
                self.strategy = DelayStrategy(strategy_str)
            except ValueError:
                pass

            self.debug_mode = os.getenv('DELAY_DEBUG', 'false').lower() == 'true'

        except ValueError:
            pass
    
    def calculate_delay(self) -> float:
        """根据策略计算延迟时间"""
        self.request_count += 1
        
        if self.strategy == DelayStrategy.UNIFORM:
            delay = random.uniform(self.min_delay, self.max_delay)
            
        elif self.strategy == DelayStrategy.NORMAL:
            # 正态分布，以base_delay为均值
            std_dev = (self.max_delay - self.min_delay) / 4
            delay = np.random.normal(self.base_delay, std_dev)
            delay = max(self.min_delay, min(self.max_delay, delay))
            
        elif self.strategy == DelayStrategy.EXPONENTIAL:
            # 指数分布，大部分时间短延迟，偶尔长延迟
            scale = self.base_delay
            delay = np.random.exponential(scale)
            delay = max(self.min_delay, min(self.max_delay, delay))
            
        elif self.strategy == DelayStrategy.PROGRESSIVE:
            # 渐进式延迟，随着请求次数增加而增加
            self.progressive_factor = min(3.0, 1.0 + (self.request_count - 1) * 0.1)
            delay = self.base_delay * self.progressive_factor
            delay = max(self.min_delay, min(self.max_delay, delay))
            
        elif self.strategy == DelayStrategy.HUMAN_LIKE:
            # 模拟人类行为：大部分时间正常延迟，偶尔突发长延迟
            if random.random() < 0.1:  # 10%概率突发延迟
                delay = random.uniform(self.max_delay * 0.8, self.max_delay * 1.5)
            elif random.random() < 0.2:  # 20%概率短延迟
                delay = random.uniform(self.min_delay, self.base_delay * 0.7)
            else:  # 70%概率正常延迟
                delay = random.uniform(self.base_delay * 0.8, self.base_delay * 1.2)
            
            delay = max(self.min_delay, min(self.max_delay * 1.5, delay))
        
        else:
            delay = self.base_delay
        
        return delay
    
    def delay(self, custom_delay: Optional[float] = None) -> float:
        """
        执行延迟
        
        Args:
            custom_delay: 自定义延迟时间，如果提供则忽略策略计算
            
        Returns:
            实际延迟时间
        """
        if custom_delay is not None:
            actual_delay = custom_delay
        else:
            actual_delay = self.calculate_delay()
        
        if actual_delay > 0:
            time.sleep(actual_delay)
        
        return actual_delay
    
    def reset_progressive(self):
        """重置渐进式延迟计数器"""
        self.request_count = 0
        self.progressive_factor = 1.0
    
    def get_stats(self) -> dict:
        """获取延迟管理器统计信息"""
        return {
            "strategy": self.strategy.value,
            "base_delay": self.base_delay,
            "min_delay": self.min_delay,
            "max_delay": self.max_delay,
            "request_count": self.request_count,
            "progressive_factor": self.progressive_factor,
            "debug_mode": self.debug_mode
        }


# 创建全局延迟管理器实例
delay_manager = DelayManager()

# 为不同服务创建专用延迟管理器
search_delay_manager = DelayManager(
    base_delay=float(os.getenv('SEARCH_DELAY_BASE', 1.5)),
    min_delay=float(os.getenv('SEARCH_DELAY_MIN', 0.8)),
    max_delay=float(os.getenv('SEARCH_DELAY_MAX', 4.0)),
    strategy=DelayStrategy(os.getenv('SEARCH_DELAY_STRATEGY', 'human_like')),
    debug_mode=os.getenv('DELAY_DEBUG', 'false').lower() == 'true'
)

user_delay_manager = DelayManager(
    base_delay=float(os.getenv('USER_DELAY_BASE', 1.0)),
    min_delay=float(os.getenv('USER_DELAY_MIN', 0.5)),
    max_delay=float(os.getenv('USER_DELAY_MAX', 2.5)),
    strategy=DelayStrategy(os.getenv('USER_DELAY_STRATEGY', 'normal')),
    debug_mode=os.getenv('DELAY_DEBUG', 'false').lower() == 'true'
)
