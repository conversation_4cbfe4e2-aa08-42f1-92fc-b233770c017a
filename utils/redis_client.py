"""Redis客户端工具类"""

import time
from typing import List
from config.settings import settings
from core.logging_config import get_logger

try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False


class RedisClient:
    """Redis客户端"""

    def __init__(self):
        self.logger = get_logger(__name__)
        self._redis = None
    
    async def connect(self) -> bool:
        if not settings.REDIS_ENABLED or not REDIS_AVAILABLE:
            return False

        try:
            redis_url = f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
            if not settings.REDIS_PASSWORD:
                redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"

            self._redis = redis.from_url(
                redis_url,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )

            await self._redis.ping()
            return True

        except Exception as e:
            self.logger.error(f"Redis连接失败: {e}")
            self._redis = None
            return False
    

    def is_connected(self) -> bool:
        """检查Redis是否连接"""
        return self._redis is not None
    
    async def set_proxy_list(self, proxies: List[str]) -> bool:
        """设置有效代理列表"""
        if not self.is_connected():
            return False
            
        try:
            # 存储代理列表
            await self._redis.delete("proxy:valid_list")
            if proxies:
                await self._redis.lpush("proxy:valid_list", *proxies)
            
            # 更新时间戳
            await self._redis.set("proxy:last_update", int(time.time()))
            return True
            
        except Exception as e:
            self.logger.error(f"设置代理列表失败: {e}")
            return False
    
    async def get_proxy_list(self) -> List[str]:
        """获取有效代理列表"""
        if not self.is_connected():
            return []
            
        try:
            proxies = await self._redis.lrange("proxy:valid_list", 0, -1)
            return proxies or []
            
        except Exception as e:
            self.logger.error(f"获取代理列表失败: {e}")
            return []
    


    
    async def is_proxy_cache_expired(self) -> bool:
        """检查代理缓存是否过期"""
        if not self.is_connected():
            return True
            
        try:
            last_update = await self._redis.get("proxy:last_update")
            if not last_update:
                return True
                
            last_update_time = int(last_update)
            current_time = int(time.time())
            expire_seconds = settings.PROXY_CACHE_EXPIRE_MINUTES * 60
            
            return (current_time - last_update_time) > expire_seconds
            
        except Exception as e:
            self.logger.error(f"检查代理缓存过期状态失败: {e}")
            return True
    

    async def clear_proxy_cache(self) -> bool:
        """清空代理缓存"""
        if not self.is_connected():
            return False
            
        try:
            await self._redis.delete("proxy:valid_list", "proxy:last_update")
            return True
            
        except Exception as e:
            self.logger.error(f"清空代理缓存失败: {e}")
            return False


# 全局Redis客户端实例
redis_client = RedisClient()
