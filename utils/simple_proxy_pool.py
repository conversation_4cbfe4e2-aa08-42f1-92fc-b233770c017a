import requests
from typing import List, Optional, Dict
from core.logging_config import get_logger
from config.settings import settings
from utils.redis_client import redis_client
from utils.proxy_validator import proxy_validator


class SimpleProxyPool:

    def __init__(self):
        self.proxies: List[str] = []
        self.current_index = 0
        self.logger = get_logger(__name__)

        self.api_url = settings.PROXY_API_URL
        self.username = settings.PROXY_USERNAME
        self.password = settings.PROXY_PASSWORD
        self.enabled = settings.PROXY_ENABLED

        self.redis_connected = False

        if self.enabled:
            self._fetch_proxies_fallback()

    async def _initialize_proxy_pool(self):
        try:
            self.redis_connected = await redis_client.connect()
            if self.redis_connected:
                await self._load_proxies_from_cache()
            else:
                self._fetch_proxies_fallback()
        except Exception as e:
            self.logger.error(f"初始化代理池失败: {e}")
            self._fetch_proxies_fallback()

    async def _load_proxies_from_cache(self):
        try:
            if await redis_client.is_proxy_cache_expired():
                await self._refresh_proxy_cache()
            else:
                cached_proxies = await redis_client.get_proxy_list()
                if cached_proxies:
                    self.proxies = cached_proxies
                else:
                    await self._refresh_proxy_cache()
        except Exception as e:
            self.logger.error(f"从缓存加载代理失败: {e}")
            self._fetch_proxies_fallback()

    async def _refresh_proxy_cache(self):
        try:
            await redis_client.clear_proxy_cache()
            raw_proxies = self._fetch_proxies_from_api()
            if not raw_proxies:
                self.logger.error("无法从API获取代理")
                return

            valid_proxies = await proxy_validator.async_validate_proxy_list(raw_proxies)
            if valid_proxies:
                await redis_client.set_proxy_list(valid_proxies)
                self.proxies = valid_proxies
        except Exception as e:
            self.logger.error(f"刷新代理缓存失败: {e}")

    def get_next_proxy(self) -> Optional[Dict[str, str]]:
        if not self.enabled or not self.proxies:
            return None

        proxy_ip_port = self.proxies[self.current_index % len(self.proxies)]
        self.current_index = (self.current_index + 1) % len(self.proxies)
        proxy_url = f"http://{self.username}:{self.password}@{proxy_ip_port}"

        return {
            'http': proxy_url,
            'https': proxy_url,
            '_proxy_ip_port': proxy_ip_port
        }

    def _fetch_proxies_from_api(self) -> List[str]:
        try:
            response = requests.get(self.api_url, timeout=10)
            if response.status_code == 200:
                proxy_text = response.text.strip()
                if proxy_text:
                    if '<br />' in proxy_text:
                        proxy_list = [line.strip() for line in proxy_text.split('<br />') if line.strip()]
                    else:
                        proxy_list = [line.strip() for line in proxy_text.split('\n') if line.strip()]
                    return proxy_list
        except Exception as e:
            self.logger.error(f"从API获取代理失败: {e}")
        return []

    def _fetch_proxies_fallback(self):
        raw_proxies = self._fetch_proxies_from_api()
        if raw_proxies:
            valid_proxies = proxy_validator.validate_proxy_list(raw_proxies, max_workers=5)
            self.proxies = valid_proxies



    def get_proxy_count(self) -> int:
        return len(self.proxies)

    def is_enabled(self) -> bool:
        return self.enabled


proxy_pool = SimpleProxyPool()
