#!/usr/bin/env python3
"""
日志监控脚本
用于实时监控ReachRadar服务的日志文件
"""

import os
import sys
import time
import argparse
from pathlib import Path
from datetime import datetime


def tail_file(file_path: Path, lines: int = 10):
    """
    显示文件的最后几行
    
    Args:
        file_path: 文件路径
        lines: 显示的行数
    """
    if not file_path.exists():
        print(f"日志文件不存在: {file_path}")
        return
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.readlines()
            if content:
                print(f"\n=== {file_path.name} (最后 {min(lines, len(content))} 行) ===")
                for line in content[-lines:]:
                    print(line.rstrip())
            else:
                print(f"\n=== {file_path.name} (空文件) ===")
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")


def follow_file(file_path: Path):
    """
    实时跟踪文件内容变化
    
    Args:
        file_path: 文件路径
    """
    if not file_path.exists():
        print(f"日志文件不存在: {file_path}")
        return
    
    print(f"正在监控: {file_path}")
    print("按 Ctrl+C 停止监控\n")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 移动到文件末尾
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    print(line.rstrip())
                else:
                    time.sleep(0.1)
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控文件失败: {e}")


def show_log_summary():
    """显示日志文件摘要"""
    log_dir = Path("logs")
    
    if not log_dir.exists():
        print("日志目录不存在")
        return
    
    print(f"=== 日志摘要 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')}) ===\n")
    
    log_files = [
        ("错误日志", "error.log"),
        ("信息日志", "info.log"),
        ("访问日志", "access.log"),
        ("调试日志", "debug.log")
    ]
    
    for name, filename in log_files:
        file_path = log_dir / filename
        if file_path.exists():
            try:
                stat = file_path.stat()
                size = stat.st_size
                mtime = datetime.fromtimestamp(stat.st_mtime)
                
                # 计算文件大小
                if size < 1024:
                    size_str = f"{size} B"
                elif size < 1024 * 1024:
                    size_str = f"{size / 1024:.1f} KB"
                else:
                    size_str = f"{size / (1024 * 1024):.1f} MB"
                
                # 统计行数
                with open(file_path, 'r', encoding='utf-8') as f:
                    line_count = sum(1 for _ in f)
                
                print(f"{name:8} | {filename:12} | {size_str:8} | {line_count:6} 行 | 修改时间: {mtime.strftime('%H:%M:%S')}")
            except Exception as e:
                print(f"{name:8} | {filename:12} | 读取失败: {e}")
        else:
            print(f"{name:8} | {filename:12} | 文件不存在")
    
    print()


def clear_logs():
    """清空所有日志文件"""
    log_dir = Path("logs")
    
    if not log_dir.exists():
        print("日志目录不存在")
        return
    
    log_files = ["error.log", "info.log", "access.log", "debug.log"]
    
    print("正在清空日志文件...")
    for filename in log_files:
        file_path = log_dir / filename
        if file_path.exists():
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("")
                print(f"已清空: {filename}")
            except Exception as e:
                print(f"清空失败 {filename}: {e}")
    
    print("日志清空完成")


def main():
    parser = argparse.ArgumentParser(description="ReachRadar 日志监控工具")
    parser.add_argument("action", choices=["summary", "tail", "follow", "clear"], 
                       help="操作类型")
    parser.add_argument("--file", "-f", choices=["error", "info", "access", "debug"],
                       help="指定日志文件类型")
    parser.add_argument("--lines", "-n", type=int, default=20,
                       help="tail 模式下显示的行数 (默认: 20)")
    
    args = parser.parse_args()
    
    # 确保在项目根目录
    if not Path("logs").exists():
        print("错误: 请在项目根目录运行此脚本")
        sys.exit(1)
    
    if args.action == "summary":
        show_log_summary()
    
    elif args.action == "tail":
        if args.file:
            file_path = Path("logs") / f"{args.file}.log"
            tail_file(file_path, args.lines)
        else:
            # 显示所有日志文件的最后几行
            log_files = ["error.log", "info.log", "access.log"]
            for filename in log_files:
                file_path = Path("logs") / filename
                tail_file(file_path, args.lines)
    
    elif args.action == "follow":
        if not args.file:
            print("follow 模式需要指定 --file 参数")
            sys.exit(1)
        
        file_path = Path("logs") / f"{args.file}.log"
        follow_file(file_path)
    
    elif args.action == "clear":
        confirm = input("确定要清空所有日志文件吗? (y/N): ")
        if confirm.lower() == 'y':
            clear_logs()
        else:
            print("操作已取消")


if __name__ == "__main__":
    main()
